<div class="o-title-box-shadow z-2 relative p-4">
  <div class="absolute right-4 flex space-x-2">
    <f-base-button
      size="small"
      title="会员列表"
      type="primary"
      @click="isShowMemberSearch = true"
    />
    <f-base-button size="small" title="清空页面" @click="clearMemberInfo" />
  </div>

  <transition name="f-memberSearchBar-fade-slide" mode="out-in">
    <div
      key="search-input"
      class="el-input el-input--suffix"
      style="width: 400px"
      v-if="memberInfo.phone === ''"
    >
      <input
        type="text"
        placeholder="输入手机号或刷实体卡"
        class="el-input__inner"
        v-model.trim="phone"
        ref="memberPhone"
        @input="bindzhkInquireMember(phone)"
      />
      <span class="el-input__suffix" @click="billingInquiryEnter">
        <span class="el-input__suffix-inner">
          <i class="el-input__icon el-icon-search"></i>
        </span>
      </span>
    </div>
    <div key="member-info" v-else>
      <div class="flex space-x-3">
        <div
          class="bg-gray-200 center text-white relative"
          style="width: 50px; height: 50px"
          :style="{
            backgroundImage: `url(${memberInfo?.pic})`,
            backgroundSize: 'cover',
          }"
        >
          <i class="el-icon-user-solid text-2xl" v-if="!memberInfo?.pic"></i>
        </div>
        <div>
          <div class="text-xl font-bold">{{ memberInfo?.member_name }}</div>
          <div class="flex items-center space-x-3">
            <div class="text-gray-500 text-sm">
              {{ memberInfo?.remarks_name }}
            </div>
            <el-tag
              size="mini"
              v-if="memberInfo.sex == 1 || memberInfo.sex == 2"
              :type="memberInfo.sex == 1 ? 'primary' : memberInfo.sex == 2 ? 'danger' : 'info'"
            >
              {{memberInfo.sex == 1 ? '男' :memberInfo.sex == 2 ? '女' : ''}}
            </el-tag>
            <el-tag v-if="memberInfo.is_vip" size="mini" type="warning">
              会员
            </el-tag>
          </div>
        </div>
      </div>
      <div class="ml-2 mt-2 flex space-x-10 text-sm">
        <div>
          <div>
            <span class="text-gray">账号/手机：</span>
            {{memberInfo.phone}}
          </div>
          <div>
            <span class="text-gray">会员编号：</span>
            {{memberInfo.member_number}}
          </div>
        </div>
        <div>
          <div>
            <span class="text-gray">累计消费：</span>
            <span class="mr--1 text-xs">￥</span>
            {{memberInfo.total | filterMoney}}
          </div>
          <div>
            <span class="text-gray">消费次数：</span>
            {{memberInfo.count}}
          </div>
        </div>
        <div>
          <div>
            <span class="text-gray">剩余卡次：</span>
            {{memberInfo.MaxNum}}
          </div>
          <div>
            <span class="text-gray">剩余余额：</span>
            <span class="mr--1 text-xs">￥</span>
            {{memberInfo.balance | filterMoney}}
          </div>
        </div>
      </div>
    </div>
  </transition>
  <member-search-dialog
    :value="isShowMemberSearch"
    :login-info="loginInfo"
    @sync-is-show-memeber-search="isShowMemberSearch = $event"
    @handle-select-member="handleMemberSelect"
  ></member-search-dialog>
</div>
