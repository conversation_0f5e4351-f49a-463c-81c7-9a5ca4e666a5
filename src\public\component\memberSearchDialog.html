<el-dialog
  title="客户列表"
  :visible.sync="isShowMemberSearch"
  :show-close="true"
  append-to-body
  width="1120px"
  top="100px"
  ref="searchMemberDialogRef"
  @close="handleCloseSearchMember"
>
  <div style="max-height: calc(100vh - 270px); overflow: auto">
    <!--搜索-->
    <div class="px-3">
      <div class="el-input el-input--suffix">
        <input
          type="text"
          v-model.trim="vipSearch"
          autocomplete="off"
          placeholder="请输入客户编号、手机号、客户名称、备注名"
          @keyup.enter.exact.stop="bindSearch"
          class="el-input__inner"
        />
        <span class="el-input__suffix" @click="bindSearch">
          <span class="el-input__suffix-inner">
            <i
              class="el-input__icon el-icon-search"
              style="cursor: pointer"
            ></i>
          </span>
        </span>
      </div>
    </div>
    <el-table
      :data="vip_info"
      @row-click="chooseMember"
      ref="vipTable"
      v-loading="searchLoading"
      :height="searchMemberTableHeight"
    >
      <el-table-column label="客户名称" width="auto">
        <template slot-scope="scope">
          <div class="flex items-center space-x-2">
            <div>
              <img
                :src="scope.row.pic || 'images/touxoang.png'"
                alt=""
                style="width: 47px; height: 47px"
              />
            </div>
            <div>
              <div class="flex items-center space-x-2">
                <div class="member_name font-bold">
                  {{scope.row.member_name}}
                </div>
                <el-tag
                  size="mini"
                  v-if="scope.row.sex == 1 || scope.row.sex == 2"
                  :type="scope.row.sex == 1 ? 'primary' : scope.row.sex == 2 ? 'danger' : 'info'"
                >
                  {{scope.row.sex == 1 ? '男' :scope.row.sex == 2 ? '女' : ''}}
                </el-tag>
              </div>
              <div class="mt-1 flex items-center space-x-2">
                <div class="text-gray text-sm">{{scope.row.remarks_name}}</div>
                <el-tag v-if="scope.row.is_vip" size="mini" type="warning">
                  会员
                </el-tag>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="手机号" width="auto">
        <template slot-scope="scope">
          <div class="text-sm">
            <span class="text-gray">账号/手机：</span>
            <span>{{scope.row.phone}}</span>
          </div>
          <div class="text-sm">
            <span class="text-gray">客户编号：</span>
            <span v-if="scope.row.member_number!=0">
              {{scope.row.member_number}}
            </span>
            <span v-else>无</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="剩余卡次" align="center" width="90">
        <template slot-scope="scope">
          <div class="font-bold">
            {{ scope.row.MaxNum == 0 ? '' : scope.row.MaxNum}}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="累计消费" width="auto">
        <template slot-scope="scope">
          <div class="pr-8 text-sm">
            <div class="flex items-baseline justify-between">
              <div class="text-gray shrink-0">金额：</div>
              <div class="text-normal flex-1 text-right font-bold">
                {{ formatMoney(scope.row.total)}}
              </div>
            </div>
            <div class="flex justify-between">
              <div class="text-gray">次数：</div>
              <div class="flex-1 text-right">
                {{scope.row.count}}
                <span class="text-gray">次</span>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="last_time" label="距上次消费" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.last_time">
            {{calculateDaysAgo(scope.row.last_time)}}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="birthday" label="生日/年龄" width="130">
        <template slot-scope="scope">
          <div class="text-sm">{{ scope.row.birthday}}</div>
          <div class="text-sm">{{ getAge(scope.row.birthday) }}</div>
        </template>
      </el-table-column>
    </el-table>
    <div class="float-right mt-2" v-if="vip_info.length>0 || searchAllCount>0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-size="searchLimit"
        :current-page="searchCurrentPage"
        layout="total, prev, pager, next, jumper"
        :total="searchAllCount"
      ></el-pagination>
    </div>
  </div>
</el-dialog>
