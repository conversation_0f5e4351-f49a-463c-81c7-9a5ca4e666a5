"use strict";
Vue.component("price-scroll-box", function (resolve, reject) {
  $.get("component/priceScrollBox/priceScrollBox.html").then(function (res) {
    resolve({
      template: res,
      props: {
        price: {
          type: [String, Number],
          default: "0.00",
        },
      },
      data: function () {
        return {
          previousPrice: "0",
          isFirstLoad: true,
        };
      },
      mounted: function () {
        // 初始化数字滚动，延迟执行确保DOM完全渲染
        setTimeout(() => {
          this.updateScrollingNumbers();
        }, 200);
      },
      watch: {
        price: function (newVal, oldVal) {
          if (newVal !== oldVal) {
            this.previousPrice = oldVal || "0";
            this.$nextTick(() => {
              this.updateScrollingNumbers();
            });
          }
          if (this.isFirstLoad) {
            this.isFirstLoad = false;
            this.$nextTick(() => {
              this.updateScrollingNumbers();
            });
          }
        },
      },
      methods: {
        // 格式化价格，当大于等于10000时添加万位标签
        formatPriceWithWanTag: function (price) {
          const numPrice = parseFloat(price || 0);
          const priceStr = numPrice.toString();
          const parts = priceStr.split(".");
          const integerPart = parts[0];
          const decimalPart = parts[1] || "00";

          if (numPrice >= 10000) {
            const integerNum = parseInt(integerPart);
            const wanPart = Math.floor(integerNum / 10000);
            const remainderPart = integerNum % 10000;

            return {
              integerPart: wanPart.toString(),
              remainderPart: remainderPart.toString().padStart(4, "0"),
              decimalPart: decimalPart,
            };
          }

          return {
            integerPart: integerPart,
            remainderPart: "",
            decimalPart: decimalPart,
          };
        },
        // 更新数字滚动动画
        updateScrollingNumbers: function () {
          this.$nextTick(() => {
            const scrollElements = this.$el.querySelectorAll(
              ".priceScrollBox_number-scroll"
            );
            scrollElements.forEach((element) => {
              const targetDigit = parseInt(element.getAttribute("data-digit"));
              const inner = element.querySelector(
                ".priceScrollBox_number-scroll-inner"
              );
              if (inner && !isNaN(targetDigit)) {
                const offset = -targetDigit * 10; // 每个数字占10%高度
                inner.style.transform = `translateY(${offset}%)`;
              }
            });
          });
        },
      },
    });
  });
});
