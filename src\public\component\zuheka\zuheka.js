"use strict";
Vue.component("app-zuheka", function (resolve, reject) {
  $.get("component/zuheka/zuheka.html").then(function (res) {
    resolve({
      template: res,
      props: {
        login: {
          type: Object,
        },
        handle_foucs_input: {
          type: Boolean,
          value: false,
        },
        "transition-direction": {
          type: String,
          value: "slide-down",
        },
      },
      //TODO
      data: function () {
        let goodsSearchConfig = null;
        let serverLimit = 9999;
        try {
          goodsSearchConfig = localStorage.getItem("goodsSearchConfig");
          if (goodsSearchConfig) {
            goodsSearchConfig = JSON.parse(goodsSearchConfig);
            serverLimit = goodsSearchConfig["serverLimit"] || 10;
          }
        } catch (e) {
          console.warn("Failed to parse goodsSearchConfig:", e);
          serverLimit = 10;
        }
        return {
          timerManager: ff_util.timerManager,
          //订单号
          orderNo: "",
          //充次卡下单完智慧跳转支付页面
          isRechargeCard: false,
          //收银台--充次卡收款跳转到收银台。 0:会员余额,1:支付宝/微信,2:现金,4:自定义收款
          isPayStatus: 4,
          //收银台到支付页面的标志位
          billToPay: 0,
          buy_receipt: false,
          //下单时候需要的充次卡信息
          extraData: {},
          //loading页面转圈圈
          loading: false,
          url: baseUrl,
          //充次卡开单详情
          zhk_server_details_name: [],
          //循环的服务列表
          leftServiceList: [],
          //搜索的关键字
          search_keyword: "",
          //备注信息
          beizhu_info: "",
          //最后需要提交支付的值
          pay_all: 0,
          pay_all_show: "0.00",
          loginInfo: {}, // 登录信息
          serverLabelid: "",
          serverPage: 1, //服务查询的页数
          serverLimit: serverLimit, //服务查询的个数
          isServerScroll: false,
          serverAllCount: 0,
          busy: false,
          loadingtip: "加载中···",
          memberInfo: {},
          //判断是不是展示销售
          isShowSelectSalesDialog: false,
          //存贮销售内容
          zhkxiaoshous: [],
          //销售用来存储以选择的销售信息。
          xiao_shou_zhanshi: [],
          //在页面展示选中的销售内容
          zhk_xiao_shou_zhanshi: [],
          //页面展示销售的变量
          SalesShow: "",
          selesShows: "请选择",
          giftPickTimeOptions: {
            disabledDate(time) {
              return time.getTime() < Date.now() - 8.64e7; //如果没有后面的-8.64e7就是不可以选择今天的
            },
          },
          // 选择协助员工
          helpStaffArr: {},
          helpStaffAll: [],
          checkHelpStaffArr: [],
          bindStaffId: 0,
          isactive1: 4,
          // 有效期等
          cardinfo: {
            permanent: "2",
            validity_time: "",
            effectiveMethod: "1",
            dueDate: "",
          },
          // 左键长按相关变量
          longPressTimer: null,
          longPressInterval: null,
          longPressFirstTimer: null,
          isLongPressing: false,
          wasLongPressed: false,
          leftPressData: null,
          leftPressEvent: null,
          leftPressStartTime: 0,
          isLeftPressed: false,
          // 右键长按相关变量
          rightLongPressTimer: null,
          rightLongPressInterval: null,
          rightLongPressFirstTimer: null,
          isRightLongPressing: false,
          wasRightLongPressed: false,
          rightLongPressData: null,
          rightLongPressEvent: null,
          rightPressStartTime: 0,
          isRightPressed: false,
          isShowMemberSearch: false,
        };
      },
      mounted: function () {
        this.serviceList();
        this.setupPriceTagHover();
        this.setupGlobalMouseEvents();
      },

      methods: {
        // 组件内部 DOM 查询辅助方法
        queryInComponent: function (selector) {
          try {
            return this.$el && this.$el.querySelectorAll
              ? this.$el.querySelectorAll(selector)
              : [];
          } catch (e) {
            console.warn("DOM query failed:", e);
            return [];
          }
        },

        // 高亮搜索关键词
        highlightSearchKeyword: function (text) {
          if (!this.search_keyword || !text) {
            return text;
          }

          // 转义特殊字符，防止正则表达式错误
          const escapedKeyword = this.search_keyword.replace(
            /[.*+?^${}()|[\]\\]/g,
            "\\$&"
          );
          const regex = new RegExp(`(${escapedKeyword})`, "gi");

          return text.replace(
            regex,
            '<span class="o-search-highlight">$1</span>'
          );
        },
        setupPriceTagHover: function () {
          // 使用事件委托来处理动态添加的元素
          this.$nextTick(() => {
            // 避免重复绑定事件
            if (this._priceTagEventsSetup) return;
            this._priceTagEventsSetup = true;

            $(document).on(
              "mouseenter.zuheka",
              ".o-price-select-tag",
              function () {
                $(this)
                  .closest(".o-service-card")
                  .addClass("price-tag-hovered");
              }
            );
            $(document).on(
              "mouseleave.zuheka",
              ".o-price-select-tag",
              function () {
                $(this)
                  .closest(".o-service-card")
                  .removeClass("price-tag-hovered");
              }
            );
          });
        },
        setupGlobalMouseEvents: function () {
          // 避免重复绑定事件
          if (this._globalEventsSetup) return;
          this._globalEventsSetup = true;

          const _self = this;
          // 全局鼠标松开事件，确保长按能正确结束
          _self.globalMouseUpHandler = function (event) {
            try {
              if (event.button === 0) {
                _self.handleGlobalLeftMouseUp(event);
              } else if (event.button === 2) {
                _self.handleGlobalRightMouseUp(event);
              }
            } catch (e) {
              console.warn("Global mouse event error:", e);
            }
          };
          document.addEventListener("mouseup", _self.globalMouseUpHandler);
        },
        handleMemberSelect: function (memberInfo) {
          this.memberInfo = memberInfo;
        },
        //调整单价
        handleUnitPriceChange: function (index) {
          try {
            const _self = this;
            if (
              !_self.zhk_server_details_name ||
              !_self.zhk_server_details_name[index]
            ) {
              console.warn("Invalid item index:", index);
              return;
            }

            const item = _self.zhk_server_details_name[index];
            const unitPrice = parseFloat(item.unitPrice) || 0;
            const quantity = parseInt(item.numberAvailable) || 1;

            // 计算小计 = 单价 * 数量
            const subtotal = (unitPrice * quantity).toFixed(2);
            item.subtotal = subtotal;

            // 更新totalAmount (以分为单位)
            item.totalAmount = Math.round(unitPrice * quantity * 100);

            // 重新计算总价
            _self.calculatePrice();
          } catch (e) {
            console.error("Error in handleUnitPriceChange:", e);
          }
        },

        //设置单价
        handleSetUnitPrice: function (index, price) {
          try {
            if (
              !this.zhk_server_details_name ||
              !this.zhk_server_details_name[index]
            ) {
              console.warn("Invalid item index:", index);
              return;
            }

            const item = this.zhk_server_details_name[index];
            const unitPrice = parseFloat(price) || 0;
            const quantity = parseInt(item.numberAvailable) || 1;

            // 设置单价
            item.unitPrice = unitPrice.toFixed(2);

            // 计算小计 = 单价 * 数量
            const subtotal = (unitPrice * quantity).toFixed(2);
            item.subtotal = subtotal;

            // 更新totalAmount (以分为单位)
            item.totalAmount = Math.round(unitPrice * quantity * 100);

            // 重新计算总价
            this.calculatePrice();

            // 触发单价输入框动画效果
            this.$nextTick(() => {
              try {
                const serviceCard =
                  this.queryInComponent(".o-service-card")[index];
                if (serviceCard) {
                  const priceInput = serviceCard.querySelector(
                    ".o-price-select-hover"
                  );
                  if (priceInput) {
                    priceInput.classList.add("o-price-update-animation");
                    this.timerManager.createTimer(
                      "priceAnimation_" + index,
                      () => {
                        if (priceInput && priceInput.classList) {
                          priceInput.classList.remove(
                            "o-price-update-animation"
                          );
                        }
                      },
                      600
                    );
                  }
                }
              } catch (e) {
                console.warn("Animation error:", e);
              }
            });
          } catch (e) {
            console.error("Error in handleSetUnitPrice:", e);
          }
        },
        //聚焦
        inputFocus: function (dom) {
          this.$nextTick(
            function () {
              dom.focus();
            }.bind(this)
          );
        },
        //clearPage 清空页面
        clearPage: function () {
          try {
            // 清理定时器
            this.timerManager.clearAll();

            this.zhk_server_details_name = [];
            this.beizhu_info = "";
            this.zhk_xiao_shou_zhanshi = [];
            this.xiao_shou_zhanshi = [];
            this.SalesShow = "";
            this.pay_all_show = "0.00";
            this.pay_all = 0;

            if (this.helpStaffArr && this.helpStaffArr[this.isactive1]) {
              this.helpStaffArr[this.isactive1] = [];
            }

            this.cardinfo = {
              permanent: "2",
              validity_time: "",
              effectiveMethod: "1",
              dueDate: "",
            };
            this.memberInfo = {};

            // 重置服务卡片的动画状态
            if (this.leftServiceList && Array.isArray(this.leftServiceList)) {
              this.leftServiceList.forEach((item) => {
                if (item) {
                  this.$set(item, "_justAdded", false);
                }
              });
            }

            if (this.$refs && this.$refs.search_keyword) {
              this.inputFocus(this.$refs.search_keyword);
            }
          } catch (e) {
            console.error("Error in clearPage:", e);
          }
        },
        //关闭收款
        bindClosePay: function (flag) {
          this.buy_receipt = flag;
          // 调用 member-search-bar 组件的 clearMemberInfo 方法
          if (this.$refs.memberSearchBar) {
            this.$refs.memberSearchBar.clearMemberInfo();
          }
          // this.clearPage();
        },
        //选择销售弹框
        zhkchange_xiaoshou: function () {
          this.isShowSelectSalesDialog = true;
          this.loading = true;
          let salemenArr = this.zhk_xiao_shou_zhanshi;
          this.xiao_shou_zhanshi = salemenArr;
          $.ajax({
            url: this.url + "/android/Staff/sellsman",
            type: "post",
            data: {
              merchantid: this.loginInfo.merchantid,
              storeid: this.loginInfo.storeid,
            },
            success: (res) => {
              if (res.code == 1) {
                this.zhkxiaoshous = res.data;
                if (salemenArr?.length != 0) {
                  for (let i = 0; i < this.zhkxiaoshous.length; i++) {
                    let flag = true;
                    for (let j = 0; j < salemenArr.length; j++) {
                      if (salemenArr[j].id == this.zhkxiaoshous[i].id) {
                        this.zhkxiaoshous[i]["is_choice_xiaoshou"] = true;
                        flag = false;
                        break;
                      }
                    }
                    if (flag) {
                      this.zhkxiaoshous[i]["is_choice_xiaoshou"] = false;
                    }
                  }
                } else {
                  for (let i = 0; i < this.zhkxiaoshous.length; i++) {
                    this.zhkxiaoshous[i]["is_choice_xiaoshou"] = false;
                  }
                }
                this.loading = false;
              } else {
                this.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
        },
        //充次卡确定销售
        handleSelectSalesSubmit: function () {
          this.SalesShow = "";
          if (this.xiao_shou_zhanshi) {
            this.zhk_xiao_shou_zhanshi = JSON.parse(
              JSON.stringify(this.xiao_shou_zhanshi)
            );
            var arrlength = this.zhk_xiao_shou_zhanshi.length;
            if (this.zhk_xiao_shou_zhanshi) {
              for (let i = 0; i < arrlength; i++) {
                if (i == arrlength - 1) {
                  this.SalesShow += this.zhk_xiao_shou_zhanshi[i]["nickname"];
                } else {
                  this.SalesShow +=
                    this.zhk_xiao_shou_zhanshi[i]["nickname"] + "、";
                }
              }
            } else {
              this.SalesShow = "";
            }
          } else {
            this.zhk_xiao_shou_zhanshi = this.xiao_shou_zhanshi;
            this.SalesShow = "";
          }
          this.xiao_shou_zhanshi = [];
          this.isShowSelectSalesDialog = false;
        },
        //充次卡取消销售
        handleSelectSalesCancel: function () {
          this.SalesShow = "";
          var arrlength = this.zhk_xiao_shou_zhanshi.length;
          if (this.zhk_xiao_shou_zhanshi) {
            for (let i = 0; i < arrlength; i++) {
              if (i == arrlength - 1) {
                this.SalesShow += this.zhk_xiao_shou_zhanshi[i]["nickname"];
              } else {
                this.SalesShow +=
                  this.zhk_xiao_shou_zhanshi[i]["nickname"] + "、";
              }
            }
          } else {
            this.SalesShow = "";
          }
          for (let i = 0; i < this.zhkxiaoshous.length; i++) {
            this.zhkxiaoshous[i]["is_choice_xiaoshou"] = false;
          }
          this.$forceUpdate();
          this.isShowSelectSalesDialog = false;
        },
        //选择销售框
        chioce_xiaoshou: function (index, data, xiao_shou_id) {
          let item = this.zhkxiaoshous[index];
          if (data == true) {
            item["is_choice_xiaoshou"] = data;
            this.xiao_shou_zhanshi.push(item);
          } else {
            for (let j = 0; j < this.xiao_shou_zhanshi.length; j++) {
              if (this.xiao_shou_zhanshi[j]["id"] == xiao_shou_id) {
                this.xiao_shou_zhanshi.splice(j, 1);
              }
            }
          }
          this.$forceUpdate();
        },

        //删除服务
        delRightCardList: function (index) {
          try {
            if (
              !this.zhk_server_details_name ||
              !this.zhk_server_details_name[index]
            ) {
              console.warn("Invalid item index for deletion:", index);
              return;
            }

            // 获取要删除的服务信息用于提示
            var deletedService = this.zhk_server_details_name[index];

            // 添加删除动画类
            this.$set(deletedService, "_removing", true);

            // 延迟删除以显示缩小动画
            this.timerManager.createTimer(
              "deleteService_" + index + "_" + Date.now(),
              () => {
                try {
                  // 删除服务项
                  if (
                    this.zhk_server_details_name &&
                    this.zhk_server_details_name[index]
                  ) {
                    this.zhk_server_details_name.splice(index, 1);
                    this.calculatePrice();
                  }
                } catch (e) {
                  console.error("Error during service deletion:", e);
                }
              },
              100
            );
          } catch (e) {
            console.error("Error in delRightCardList:", e);
          }
        },

        //自动查询服务
        serviceList: function (flag) {
          try {
            this.loading = true;
            $.ajax({
              url: this.url + "/android/Service/serviceList",
              type: "post",
              data: {
                keyword: this.search_keyword || "",
                labelid: this.serverLabelid || "",
                sort: 1, // 默认正序，1正序 2倒序
                status: 1, // 1上架。  2下架
                storeid: this.loginInfo.storeid,
                page: 1,
                limit: 999999,
              },
              success: (res) => {
                try {
                  this.loading = false;
                  if (res && res.code == 0) {
                    this.serverAllCount = res.count || 0;
                    if (this.serverAllCount < 10) {
                      this.busy = false;
                    }

                    const newData = res.data || [];
                    if (flag == 1) {
                      this.leftServiceList = (
                        this.leftServiceList || []
                      ).concat(newData);
                      if (res.count == 0) {
                        this.isServerScroll = true;
                      } else {
                        this.isServerScroll = false;
                      }
                    } else {
                      this.leftServiceList = newData;
                      this.isServerScroll = false;
                    }

                    //获取数据后然后对数据赋值这个时候将查不到数据的div属性换掉
                    //以下在加载服务时候给没一个服务添加一个服务人员id用来存储后面要穿的值
                    if (
                      this.leftServiceList &&
                      Array.isArray(this.leftServiceList)
                    ) {
                      for (let i = 0; i < this.leftServiceList.length; i++) {
                        if (this.leftServiceList[i]) {
                          this.leftServiceList[i]["technician_id"] = [];
                          this.leftServiceList[i]["salesmen"] = [];
                          this.leftServiceList[i]["num"] = 1;
                          this.leftServiceList[i]["manualDiscount"] = 1;
                          this.leftServiceList[i]["discount"] = 1;
                          this.leftServiceList[i]["manualDiscountCard"] = {};
                          this.leftServiceList[i]["zhonglei"] = 1; //添加一个字段用来判断种类
                          this.leftServiceList[i]["_animationId"] =
                            Date.now() + i; //添加动画标识
                        }
                      }
                    }
                  } else {
                    this.leftServiceList = [];
                    if (this.$message) {
                      this.$message({
                        type: "error",
                        message: "暂无数据",
                        duration: 1500,
                      });
                    }
                  }
                } catch (e) {
                  console.error("Error processing service list response:", e);
                  this.loading = false;
                }
              },
              error: (xhr, status, error) => {
                console.error("Service list request failed:", error);
                this.loading = false;
              },
            });
          } catch (e) {
            console.error("Error in serviceList:", e);
            this.loading = false;
          }
        },

        // 开始连续添加
        startContinuousAdd: function () {
          if (!this.isLongPressing || !this.isLeftPressed) return;

          // 第一次+10在200ms后执行
          this.longPressFirstTimer = this.timerManager.createTimer(
            "longPressFirst",
            () => {
              if (!this.isLongPressing || !this.isLeftPressed) return;

              // 第一次添加10个数量
              this.addServiceQuantity(
                this.leftPressData,
                this.leftPressEvent,
                10
              );

              // 后续每1000ms执行一次
              this.longPressInterval = this.timerManager.createTimer(
                "longPressInterval",
                () => {
                  if (!this.isLongPressing || !this.isLeftPressed) {
                    this.timerManager.clearTimer("longPressInterval");
                    return;
                  }

                  // 添加10个数量
                  this.addServiceQuantity(
                    this.leftPressData,
                    this.leftPressEvent,
                    10
                  );
                },
                1000,
                "interval"
              );
            },
            200
          );
        },
        // 添加服务数量（支持指定数量）
        addServiceQuantity: function (value, event, quantity = 1) {
          try {
            if (!value || !value.id) {
              console.warn("Invalid service data");
              return;
            }

            const data = value;
            var zhk_price;
            var priceIndex = data["price"] ? data["price"].indexOf("-") : -1;
            if (priceIndex == -1) {
              zhk_price = parseFloat(data["price"]) || 0;
            } else {
              zhk_price = parseFloat(data["price"].slice(priceIndex + 1)) || 0;
            }

            // 查找是否已存在该服务
            let duplicateIndex = -1;
            if (
              this.zhk_server_details_name &&
              Array.isArray(this.zhk_server_details_name)
            ) {
              for (let i = 0; i < this.zhk_server_details_name.length; i++) {
                if (
                  this.zhk_server_details_name[i] &&
                  data["id"] == this.zhk_server_details_name[i]["id"]
                ) {
                  duplicateIndex = i;
                  break;
                }
              }
            }

            if (duplicateIndex !== -1) {
              // 如果服务已存在，增加指定数量
              const item = this.zhk_server_details_name[duplicateIndex];
              const newQuantity = Math.min(
                (item.numberAvailable || 0) + quantity,
                9999
              );
              const actualAdded = newQuantity - (item.numberAvailable || 0);

              if (actualAdded > 0) {
                item.numberAvailable = newQuantity;

                // 重新计算价格
                const unitPrice = parseFloat(item.unitPrice) || 0;
                const subtotal = (unitPrice * item.numberAvailable).toFixed(2);
                item.subtotal = subtotal;
                item.totalAmount = Math.round(
                  unitPrice * item.numberAvailable * 100
                );

                // 显示弹出数字效果
                if (
                  event &&
                  event.clientX !== undefined &&
                  event.clientY !== undefined
                ) {
                  this.createPopupNumber(
                    event.clientX,
                    event.clientY,
                    newQuantity,
                    quantity >= 10,
                    false
                  );
                }

                // 滚动到对应项并高亮
                this.$nextTick(() => {
                  try {
                    const serviceCards =
                      this.queryInComponent(".o-service-card");
                    if (serviceCards && serviceCards[duplicateIndex]) {
                      serviceCards[duplicateIndex].scrollIntoView({
                        behavior: "smooth",
                        block: "center",
                      });

                      serviceCards[duplicateIndex].classList.add(
                        "o-service-card-highlight"
                      );
                      this.timerManager.createTimer(
                        "highlight_" + duplicateIndex + "_" + Date.now(),
                        () => {
                          if (
                            serviceCards[duplicateIndex] &&
                            serviceCards[duplicateIndex].classList
                          ) {
                            serviceCards[duplicateIndex].classList.remove(
                              "o-service-card-highlight"
                            );
                          }
                        },
                        1000
                      );
                    }
                  } catch (e) {
                    console.warn("Scroll/highlight error:", e);
                  }
                });

                this.calculatePrice();
              }
            } else {
              // 如果服务不存在，添加新服务
              const newData = JSON.parse(JSON.stringify(data));
              newData["numberAvailable"] = quantity;
              newData["zhk_price_show"] = zhk_price;
              newData["zhk_price"] = zhk_price * 100;
              newData["unitPrice"] = zhk_price.toFixed(2);
              newData["totalAmount"] = zhk_price * quantity * 100;
              newData["subtotal"] = (zhk_price * quantity).toFixed(2);
              newData["_animationId"] = Date.now() + Math.random();

              this.$set(value, "_justAdded", true);
              this.timerManager.createTimer(
                "justAdded_" + data.id + "_" + Date.now(),
                () => {
                  if (value) {
                    this.$set(value, "_justAdded", false);
                  }
                },
                600
              );

              if (!this.zhk_server_details_name) {
                this.zhk_server_details_name = [];
              }
              this.zhk_server_details_name.unshift(newData);

              // 显示弹出数字效果
              if (
                event &&
                event.clientX !== undefined &&
                event.clientY !== undefined
              ) {
                this.createPopupNumber(
                  event.clientX,
                  event.clientY,
                  quantity,
                  quantity >= 10,
                  false
                );
              }

              // 滚动到顶端
              this.$nextTick(() => {
                try {
                  const serviceCardsContainers =
                    this.queryInComponent(".o-scrollbar");
                  const rightScrollContainer =
                    serviceCardsContainers && serviceCardsContainers[1];
                  if (rightScrollContainer && rightScrollContainer.scrollTo) {
                    rightScrollContainer.scrollTo({
                      top: 0,
                      behavior: "smooth",
                    });
                  }
                } catch (e) {
                  console.warn("Scroll error:", e);
                }
              });

              this.calculatePrice();
            }
          } catch (e) {
            console.error("Error in addServiceQuantity:", e);
          }
        },
        // 统一的鼠标按下处理
        handleMouseDown: function (value, event) {
          try {
            if (!event || !value) return;

            event.preventDefault();
            event.stopPropagation();

            if (event.button === 0) {
              // 左键按下
              this.handleLeftMouseDown(value, event);
            } else if (event.button === 2) {
              // 右键按下
              this.handleRightMouseDown(value, event);
            }
          } catch (e) {
            console.error("Error in handleMouseDown:", e);
          }
        },

        // 左键按下处理
        handleLeftMouseDown: function (value, event) {
          try {
            // 清理之前的状态
            this.clearLeftPressTimers();

            this.leftPressStartTime = performance.now();
            this.leftPressData = value;
            this.leftPressEvent = event;
            this.isLeftPressed = true;
            this.isLongPressing = false;
            this.wasLongPressed = false;

            // 400ms后开始长按模式
            this.longPressTimer = this.timerManager.createTimer(
              "longPressStart",
              () => {
                if (this.isLeftPressed) {
                  this.isLongPressing = true;
                  this.startContinuousAdd();
                }
              },
              400
            );
          } catch (e) {
            console.error("Error in handleLeftMouseDown:", e);
          }
        },

        // 右键按下处理
        handleRightMouseDown: function (value, event) {
          try {
            // 清理之前的状态
            this.clearRightLongPressTimers();

            this.rightPressStartTime = performance.now();
            this.rightLongPressData = value;
            this.rightLongPressEvent = event;
            this.isRightPressed = true;
            this.isRightLongPressing = false;
            this.wasRightLongPressed = false;

            // 400ms后开始右键长按模式
            this.rightLongPressTimer = this.timerManager.createTimer(
              "rightLongPressStart",
              () => {
                if (this.isRightPressed) {
                  this.isRightLongPressing = true;
                  this.startRightContinuousRemove();
                }
              },
              400
            );
          } catch (e) {
            console.error("Error in handleRightMouseDown:", e);
          }
        },

        // 统一的鼠标松开处理
        handleMouseUp: function (event) {
          if (event.button === 0) {
            this.handleLeftMouseUp(event);
          } else if (event.button === 2) {
            this.handleRightMouseUp(event);
          }
        },

        // 左键松开处理
        handleLeftMouseUp: function (event) {
          if (!this.isLeftPressed) return;

          const endTime = performance.now();
          const pressDuration = endTime - (this.leftPressStartTime || 0);
          const wasInLongPressMode = this.isLongPressing;

          // 清理状态和定时器
          this.isLeftPressed = false;
          this.clearLeftPressTimers();

          // 如果是短按且没有进入长按模式，执行单击逻辑
          if (pressDuration < 400 && !wasInLongPressMode) {
            this.addServiceQuantity(this.leftPressData, this.leftPressEvent, 1);
          } else if (wasInLongPressMode) {
            // 设置长按结束标记
            this.wasLongPressed = true;
            this.timerManager.createTimer(
              "resetLongPressed",
              () => {
                this.wasLongPressed = false;
              },
              100
            );
          }

          this.isLongPressing = false;
        },

        // 右键松开处理
        handleRightMouseUp: function (event) {
          if (!this.isRightPressed) return;

          const endTime = performance.now();
          const pressDuration = endTime - (this.rightPressStartTime || 0);
          const wasInLongPressMode = this.isRightLongPressing;

          // 清理状态和定时器
          this.isRightPressed = false;
          this.clearRightLongPressTimers();

          // 如果是短按且没有进入长按模式，执行单击逻辑
          if (pressDuration < 400 && !wasInLongPressMode) {
            this.removeServiceQuantity(
              this.rightLongPressData,
              this.rightLongPressEvent,
              1
            );
          } else if (wasInLongPressMode) {
            // 设置长按结束标记
            this.wasRightLongPressed = true;
            this.timerManager.createTimer(
              "resetRightLongPressed",
              () => {
                this.wasRightLongPressed = false;
              },
              100
            );
          }

          this.isRightLongPressing = false;
        },

        // 全局左键松开处理
        handleGlobalLeftMouseUp: function (event) {
          if (this.isLeftPressed || this.isLongPressing) {
            this.handleLeftMouseUp(event);
          }
        },

        // 全局右键松开处理
        handleGlobalRightMouseUp: function (event) {
          if (this.isRightPressed || this.isRightLongPressing) {
            this.handleRightMouseUp(event);
          }
        },

        // 清理左键相关定时器
        clearLeftPressTimers: function () {
          try {
            if (this.timerManager && this.timerManager.clearTimer) {
              this.timerManager.clearTimer("longPressStart");
              this.timerManager.clearTimer("longPressFirst");
              this.timerManager.clearTimer("longPressInterval");
            }
            this.longPressTimer = null;
            this.longPressFirstTimer = null;
            this.longPressInterval = null;
          } catch (e) {
            console.warn("Error clearing left press timers:", e);
          }
        },

        // 清除右键长按定时器
        clearRightLongPressTimers: function () {
          try {
            if (this.timerManager && this.timerManager.clearTimer) {
              this.timerManager.clearTimer("rightLongPressStart");
              this.timerManager.clearTimer("rightLongPressFirst");
              this.timerManager.clearTimer("rightLongPressInterval");
            }
            this.rightLongPressTimer = null;
            this.rightLongPressFirstTimer = null;
            this.rightLongPressInterval = null;
          } catch (e) {
            console.warn("Error clearing right press timers:", e);
          }
        },

        // 处理右键点击
        handleRightClick: function (value, event) {
          // 阻止右键菜单，实际逻辑在mousedown/mouseup中处理
          event.preventDefault();
          event.stopPropagation();
          return false;
        },
        // 开始右键连续减少
        startRightContinuousRemove: function () {
          if (!this.isRightLongPressing || !this.isRightPressed) return;

          // 第一次-10在200ms后执行
          this.rightLongPressFirstTimer = this.timerManager.createTimer(
            "rightLongPressFirst",
            () => {
              if (!this.isRightLongPressing || !this.isRightPressed) return;

              // 第一次减少10个数量
              this.removeServiceQuantity(
                this.rightLongPressData,
                this.rightLongPressEvent,
                10
              );

              // 后续每1000ms执行一次
              this.rightLongPressInterval = this.timerManager.createTimer(
                "rightLongPressInterval",
                () => {
                  if (!this.isRightLongPressing || !this.isRightPressed) {
                    this.timerManager.clearTimer("rightLongPressInterval");
                    return;
                  }

                  // 减少10个数量
                  this.removeServiceQuantity(
                    this.rightLongPressData,
                    this.rightLongPressEvent,
                    10
                  );
                },
                1000,
                "interval"
              );
            },
            200
          );
        },
        // 减少服务数量（支持指定数量）
        removeServiceQuantity: function (value, event, quantity = 1) {
          const data = value;

          // 查找是否存在该服务
          let existingIndex = -1;
          for (let i = 0; i < this.zhk_server_details_name.length; i++) {
            if (data["id"] == this.zhk_server_details_name[i]["id"]) {
              existingIndex = i;
              break;
            }
          }

          if (existingIndex !== -1) {
            const item = this.zhk_server_details_name[existingIndex];
            const newQuantity = Math.max(item.numberAvailable - quantity, 0);
            const actualRemoved = item.numberAvailable - newQuantity;

            if (actualRemoved > 0) {
              if (newQuantity === 0) {
                // 如果数量变为0，删除该服务项
                this.$set(item, "_removing", true);
                this.timerManager.createTimer(
                  "removeService_" + existingIndex,
                  () => {
                    this.zhk_server_details_name.splice(existingIndex, 1);
                    this.calculatePrice();
                  },
                  100
                );
              } else {
                // 更新数量
                item.numberAvailable = newQuantity;

                // 重新计算价格
                const unitPrice = parseFloat(item.unitPrice) || 0;
                const subtotal = (unitPrice * item.numberAvailable).toFixed(2);
                item.subtotal = subtotal;
                item.totalAmount = Math.round(
                  unitPrice * item.numberAvailable * 100
                );

                this.calculatePrice();
              }

              // 显示弹出数字效果（负数）
              if (event) {
                this.createPopupNumber(
                  event.clientX,
                  event.clientY,
                  newQuantity,
                  quantity >= 10,
                  true
                );
              }

              // 滚动到对应项并高亮
              if (newQuantity > 0) {
                this.$nextTick(() => {
                  const serviceCards = this.queryInComponent(".o-service-card");
                  if (serviceCards[existingIndex]) {
                    serviceCards[existingIndex].scrollIntoView({
                      behavior: "smooth",
                      block: "center",
                    });

                    serviceCards[existingIndex].classList.add(
                      "o-service-card-highlight"
                    );
                    this.timerManager.createTimer(
                      "rightHighlight_" + existingIndex,
                      () => {
                        serviceCards[existingIndex].classList.remove(
                          "o-service-card-highlight"
                        );
                      },
                      1000
                    );
                  }
                });
              }
            }
          }
        },
        //
        /**
         * 创建弹出数字效果
         * @param {*} x 鼠标位置
         * @param {*} y 鼠标位置
         * @param {*} number 显示的内容
         * @param {*} isLongPress 是否长按
         * @param {*} isDecrease 是否减少
         */
        createPopupNumber: function (
          x,
          y,
          number,
          isLongPress = false,
          isDecrease = false
        ) {
          try {
            if (typeof x !== "number" || typeof y !== "number") {
              console.warn("Invalid coordinates for popup");
              return;
            }

            const popup = document.createElement("div");
            const popupAdd = document.createElement("div");

            // 根据操作类型和模式设置样式类
            let className = "o-number-popup ";
            let classNameAdd = "o-number-popup-add ";
            // 单击用蓝色，长按用紫色
            className += isLongPress
              ? "o-number-popup-purple"
              : "o-number-popup-blue";

            if (isDecrease) {
              // 减少操作：单击用蓝色，长按用红色
              classNameAdd += "o-number-popup-red";
              popupAdd.textContent = isLongPress ? "-10" : "-1";
            } else {
              // 增加操作：单击用蓝色，长按用红色
              classNameAdd += isLongPress
                ? "o-number-popup-purple"
                : "o-number-popup-blue";
              popupAdd.textContent = isLongPress ? "+10" : "+1";
            }

            popup.className = className;
            popupAdd.className = classNameAdd;
            popup.textContent = String(number);
            popup.style.left = x + "px";
            popupAdd.style.left = x + "px";
            popup.style.top = y + "px";
            popupAdd.style.top = y + "px";

            if (document.body) {
              document.body.appendChild(popup);
              document.body.appendChild(popupAdd);

              // 动画结束后移除元素
              this.timerManager.createTimer(
                "popupCleanup_" + Date.now() + "_" + Math.random(),
                () => {
                  try {
                    if (popup && popup.parentNode) {
                      popup.parentNode.removeChild(popup);
                    }
                    if (popupAdd && popupAdd.parentNode) {
                      popupAdd.parentNode.removeChild(popupAdd);
                    }
                  } catch (e) {
                    console.warn("Error removing popup elements:", e);
                  }
                },
                1000
              );
            }
          } catch (e) {
            console.error("Error creating popup number:", e);
          }
        },
        //每次添加服务和删除服务都会触发价格的事件
        calculatePrice: function () {
          try {
            this.pay_all = 0;
            if (
              !this.zhk_server_details_name ||
              this.zhk_server_details_name.length == 0
            ) {
              this.pay_all_show = "0.00";
            } else {
              for (let i = 0; i < this.zhk_server_details_name.length; i++) {
                const item = this.zhk_server_details_name[i];
                if (item && typeof item["totalAmount"] === "number") {
                  this.pay_all += item["totalAmount"];
                }
              }
              this.pay_all_show = (this.pay_all / 100).toFixed(2);
            }
          } catch (e) {
            console.error("Error calculating price:", e);
            this.pay_all_show = "0.00";
            this.pay_all = 0;
          }
        },
        loadMoreProduct: function () {
          this.isServerScroll = true;
          if (this.serverAllCount == this.leftServiceList.length) {
            this.loadingtip = "数据已全部加载";
            return false;
          } else {
            this.busy = true;
            this.loadingtip = "加载中···";
            this.serverPage++;
            this.serviceList(1);
          }
        },
        //减少充次卡的次数
        jianshao: function (index) {
          const item = this.zhk_server_details_name[index];

          if (item.numberAvailable > 1) {
            item.numberAvailable--;
          }

          // 根据单价和数量计算小计
          const unitPrice = parseFloat(item.unitPrice) || 0;
          const subtotal = (unitPrice * item.numberAvailable).toFixed(2);
          item.subtotal = subtotal;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(unitPrice * item.numberAvailable * 100);

          this.calculatePrice();
        },
        //增加充次卡的次数
        zengjia: function (index) {
          const item = this.zhk_server_details_name[index];

          if (item.numberAvailable < 9999) {
            item.numberAvailable++;
          }

          // 根据单价和数量计算小计
          const unitPrice = parseFloat(item.unitPrice) || 0;
          const subtotal = (unitPrice * item.numberAvailable).toFixed(2);
          item.subtotal = subtotal;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(unitPrice * item.numberAvailable * 100);

          this.calculatePrice();
        },
        handleNumInputChange: function (e, index) {
          const item = this.zhk_server_details_name[index];
          const numValue = parseInt(e.target.value);

          // 验证输入值是否为有效数字
          if (isNaN(numValue)) {
            // 如果输入无效，重置为1
            item.numberAvailable = 1;
          } else {
            // 限制数量范围在1-9999之间
            if (numValue < 1) {
              item.numberAvailable = 1;
            } else if (numValue > 9999) {
              item.numberAvailable = 9999;
            } else {
              item.numberAvailable = numValue;
            }
          }

          // 根据单价和数量计算小计
          const unitPrice = parseFloat(item.unitPrice) || 0;
          const subtotal = (unitPrice * item.numberAvailable).toFixed(2);
          item.subtotal = subtotal;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(unitPrice * item.numberAvailable * 100);

          // 重新计算总价
          this.calculatePrice();
        },

        //调整小计
        handleSubtotalChange: function (index) {
          const item = this.zhk_server_details_name[index];
          const subtotal = parseFloat(item.subtotal) || 0;
          const quantity = parseInt(item.numberAvailable) || 1;

          // 根据小计和数量计算单价 = 小计 / 数量
          const unitPrice =
            quantity > 0 ? (subtotal / quantity).toFixed(2) : "0.00";
          item.unitPrice = unitPrice;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(subtotal * 100);

          // 重新计算总价
          this.calculatePrice();
        },

        //充次卡收款
        goSettle: function (flag) {
          var saveorderData = this.zhk_server_details_name;
          var orderItems = [];

          //获取销售id
          var salesid = this.zhk_xiao_shou_zhanshi.map((item) => item["id"]);

          for (var i = 0; i < saveorderData.length; i++) {
            orderItems.push({
              // cardName: save_orderData[i].manualDiscountCard['cardName'] || '',
              // cardName: '',
              // cardDetailsId: save_orderData[i].manualDiscountCard['id'] || 0,     // (开单使用)使用卡项详情的id
              cardDetailsId: 0, // 【可选】(开单使用)使用卡项详情的id
              // cardId: save_orderData[i].manualDiscountCard['membercard_id'] || 0,   // 卡项id
              cardId: 0, // 【可选】卡项id
              // discount: save_orderData[i].manualDiscountCard['discount'] || '10', // 折扣（开单选择充值卡有）
              discount: 1, // 【可选】折扣（开单选择充值卡有）
              equityType: 1, // 【可选】1 无权益 2折扣 3抵扣 4手动改价
              goodsId: saveorderData[i].id, // 【必填】商品id 服务，产品卡项都填写id
              itemId: 0, // 【可选】
              itemImgId: saveorderData[i].itemImgId || "0", // 【可选】预览图id
              itemName: saveorderData[i].service_name, // 【必填】商品名称
              itemType: 1, // 【必填】1 服务 2产品 3卡项 4充值
              num: saveorderData[i].numberAvailable, // 【必填】数量
              originPrice: Math.round(saveorderData[i].unitPrice * 100), // 【必填】单价  原价( 分 )
              average_price: Math.round(saveorderData[i].unitPrice * 100), // 【必填】新单价  原价( 分 )
              recharge_money: 0, // 【条件必填】充值金额（本金）金额 （分） itemType=4时必传
              realPay: Math.round(saveorderData[i].subtotal * 100), // 【必填】小计（分）
              present_money: 0, // 【条件必填】充值（赠送）金额 (分) itemType=4时必传
              salesmen: salesid || [], // 【可选】选择的销售id
              skuId: 0, // 【可选】规格id，非规格天写0
              skuName: "", // 【可选】规格名称（如：红色,大）没有填空字符串
              stage: "1", // 【可选】当前阶段（填写1）取单会返回该字段，只有1可删除、编辑权益优惠信息
              technicians: [], // 【可选】服务人员id
            });
          }

          //充次卡收款需要判断是不是要加会员
          if (!this.memberInfo.id) {
            this.$message({
              type: "warning ",
              message: "请选择会员",
              duration: 1500,
            });
            return;
          }

          if (!saveorderData.length) {
            this.$message({
              type: "warning ",
              message: "请选择服务",
              duration: 2500,
            });
            return;
          }

          this.extraData = {};
          if (this.cardinfo.permanent == 1) {
            this.extraData["indate"] = 0;
          }

          if (this.cardinfo.permanent == 2) {
            if (!this.cardinfo.validity_time) {
              this.$message({
                type: "warning ",
                message: "请输入有效天数",
                duration: 2500,
              });
              return;
            }
            this.extraData["indate"] = 0;
            this.extraData["effectiveMethod"] = this.cardinfo.effectiveMethod;
            this.extraData["validity_time"] = this.cardinfo.validity_time;
          }

          if (this.cardinfo.permanent == 3) {
            if (!this.cardinfo.zuheka_validity) {
              this.$message({
                type: "warning ",
                message: "请选择到期日期",
                duration: 2500,
              });
              return;
            }
            this.extraData["indate"] = this.cardinfo.zuheka_validity;
          }

          this.extraData["comboCardMoney"] = parseInt(
            Number(this.pay_all_show) * 100
          );

          if (this.helpStaffArr[this.isactive1]) {
            this.extraData["help_staff"] = this.helpStaffArr[
              this.isactive1
            ].map((item) => item["id"]);
          }

          $.ajax({
            url: this.url + "/android/order/orderSave",
            type: "post",
            data: {
              // addressInfo: "", // 【可选】收货地址信息
              // bookerid: 0, // 【可选】预约id 来源是预约时使用
              buyerId: this.memberInfo.id, // 【必填】用户id (orderType为3,4,5,7,8时必填)
              cashierId: this.loginInfo.id, // 【必填】收银员id
              // dispatchFee: 0, // 【可选】运费 （分）
              // dispatchType: 0, // 【可选】配送类型： 1，到店自提，2，配送，0，非配送
              merchantid: this.loginInfo.merchantid, // 【必填】商户id
              orderGiftItems: [], // 【可选】订单礼品数组（预留字段）
              orderItems: JSON.stringify(orderItems), // 【必填】订单的详情信息
              // orderNo: 0, // 【可选】订单号（取单时需要传）
              orderType: 5, // 【必填】1：服务；2：产品；3：套餐卡；4：充值 5：充次卡；6 直接收款
              promotions: [], // 【必填】预留字段（优惠信息）
              // presentData: JSON.stringify([]), // 【可选】赠送项目
              remark: this.beizhu_info, // 【可选】订单备注
              sourceType: 1, // 【必填】来源类型 1,开单，2,预约，3,取单
              storeid: this.loginInfo.storeid, // 【必填】店铺id
              totalPay: Math.round(this.pay_all_show * 100), // 【可选，后台用于打印日志看】订单总价（分）
              shift_no: this.loginInfo.shift_no, // 【必填】班次单号 (android接口必填)
              extraData: JSON.stringify(this.extraData), // 【可选】充次卡额外参数
            },
            success: (res) => {
              this.loading = false;
              if (res.code != 1) {
                this.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
                return;
              }

              this.helpStaffArr[this.isactive1] = [];
              if (flag == "receipt") {
                //收款
                this.buy_receipt = true;
                this.loading = false;
                this.orderNo = res.data.orderNo;
                this.orderId = res.data.id;
                this.isRechargeCard = true;
                this.zhk_server_details_name = [];
                this.billToPay = 1;
                this.isPayStatus = 0; // 开启会员支付
              }
            },
            error: () => {
              this.loading = false;
            },
          });
        },
      },
      filters: {
        ellipsis(value) {
          if (!value) return "";
          if (value.length > 12) {
            return value.slice(0, 12) + "...";
          }
          return value;
        },

        // 格式化充值金额/100
        filterMoney: function (money) {
          money = money ? money : 0;
          return (money / 100).toFixed(2);
        },
      },
      watch: {
        login: {
          handler(n) {
            this.loginInfo = n;
          },
          deep: true,
          immediate: true,
        },
      },
      beforeDestroy: function () {
        try {
          // 清理所有管理的定时器
          if (this.timerManager && this.timerManager.clearAll) {
            this.timerManager.clearAll();
          }

          // 清理事件监听器 - 使用命名空间避免影响其他组件
          $(document).off(".zuheka");

          // 清理长按定时器
          this.clearLeftPressTimers();
          this.clearRightLongPressTimers();

          // 清理全局事件监听器
          if (this.globalMouseUpHandler) {
            document.removeEventListener("mouseup", this.globalMouseUpHandler);
            this.globalMouseUpHandler = null;
          }

          // 重置状态标记
          this._priceTagEventsSetup = false;
          this._globalEventsSetup = false;

          // 清理可能的循环引用
          this.leftPressData = null;
          this.leftPressEvent = null;
          this.rightLongPressData = null;
          this.rightLongPressEvent = null;
        } catch (e) {
          console.error("Error in beforeDestroy:", e);
        }
      },
    });
  });
});
