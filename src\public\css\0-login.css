.login-Wrap {
  position: relative;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  background: #fff;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.drag {
  -webkit-app-region: drag;
}

.no-drag {
  -webkit-app-region: no-drag;
}

.close {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
  font-size: 24px;
}

.bg_login {
  /* background-size: cover; */
  position: relative;
  top: 0px;
  left: 0px;
  /* background: url('../images/bg_login.png') no-repeat; */
  background: #fff;
  width: 100%;
  height: 100vh;
}

.input_login {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 8px;
  background: #fff;
  padding: 40px;
  width: 400px;
}

.login_choice {
  margin-bottom: 70px;
  font-weight: 600;
  font-size: 24px;
}

.form-wrap {
  box-sizing: border-box;
}

.password_font {
  /*border-right: 2px solid #E5E5E5;*/
  box-sizing: border-box;
  padding-right: 15px;
  color: rgba(51, 51, 51, 1);
  font-weight: 500;
  font-size: 20px;
}

.password_font:hover {
  cursor: pointer;
}

.code_font:hover {
  cursor: pointer;
}

.code_font {
  box-sizing: border-box;
  padding-left: 10px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 18px;
}

.telephone {
  border: 1px solid rgba(229, 229, 229, 1);
  background: rgba(255, 255, 255, 1);
  width: 100%;
  height: 45px;
}

.telephone span {
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 16px;
  line-height: 45px;
}

.password {
  margin-top: 30px;
  border: 1px solid rgba(229, 229, 229, 1);
  background: rgba(255, 255, 255, 1);
  width: 100%;
  height: 45px;
}

.password span {
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 24px;
  line-height: 45px;
}

.password img {
  display: inline-block;
  width: 37px;
  height: 28px;
}

.password img:hover {
  cursor: pointer;
}

.login {
  box-sizing: border-box;
  padding: 15px 0;
  width: 100%;
  font-size: 16px;
}

.login-label {
  display: inline-block;
  cursor: pointer;
  box-sizing: border-box;
  padding-left: 10px;
  width: 70px;
  height: 48px;
  color: #333;
  font-size: 16px;
  line-height: 48px;
  text-align: left;
}

.el-input--prefix .el-input__inner {
  padding-left: 70px;
}

.el-input__inner {
  height: 48px;
  line-height: 48px;
}

.el-button--primary.is-disabled,
.el-button--primary.is-disabled:active,
.el-button--primary.is-disabled:focus,
.el-button--primary.is-disabled:hover {
  border-color: #3363ff;
  background-color: #3363ff;
  color: #fff;
}

.version {
  position: absolute;
  bottom: 10px;
  font-size: 14px;
}

.version-title {
  margin-bottom: 6px;
  font-size: 16px;
}

.version-ul {
  box-sizing: border-box;
  padding-left: 15px;
  font-size: 16px;
}
.version-ul li {
  margin-bottom: 5px;
  line-height: 20px;
  font-family: "Microsoft YaHei", "SimHei", "SimSun";
}

.logo_set {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 45px;
}
.logo-img {
  display: block;
  max-width: 61px;
  max-height: 60px;
}
.logo-text {
  padding-left: 20px;
  color: #3363ff;
  font-size: 24px;
}

.el-dialog__header {
  padding: 0;
}
.el-dialog--center .el-dialog__body {
  padding: 25px 25px 0 25px;
}
.el-dialog__wrapper {
  overflow: hidden;
}
