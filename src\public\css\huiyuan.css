div {
  -moz-user-select: none; /*mozilar*/
  -webkit-user-select: none; /*webkit*/
  -ms-user-select: none; /*IE*/
  user-select: none;
}

/*以上是导航条样式*/

.main-caontainer {
  width: calc(100vw - 110px);
  height: 100vh;
}
.add_member_bg {
  margin-bottom: 0;
  background-attachment: fixed;
  padding-top: 10px;
  width: 100%;
  height: 100%;
  overflow-y: auto;
}
.add_member_bg::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}
.add_member_bg::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.add_member_bg::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #f1f1f1;
}

.main {
  display: flex;
  overflow: hidden;
  /* background: #f1f1f1; */
}

.left {
  height: 100vh;
  overflow: hidden;
}

/* 主体--右侧 */
.main-right {
  flex: 1;
}

.server {
  width: 500px;
  height: calc(100vh - 90px);
  /*overflow: hidden;*/
  /*background-color: yellow;*/
}

.server_chioce {
  border: 1px solid rgba(255, 255, 255, 1);
  width: 580px;
  height: 90px;
}

.server_center {
  display: flex;
  margin: auto;
  margin-top: 20px;
  width: 240px;
  height: 50px;
}

.b_left {
  border: 1px solid #3363ff;
  border-radius: 8px 0px 0px 8px;
  width: 120px;
  height: 50px;
  color: #3363ff;
  line-height: 50px;
  text-align: center;
}

.b_left:hover {
  cursor: pointer;
}

.b_right {
  /*border: 1px solid green;*/
  border: 1px solid #3363ff;
  border-radius: 0px 8px 8px 0px;
  width: 120px;
  height: 50px;
  color: #3363ff;
  line-height: 50px;
  text-align: center;
}

.b_right {
  cursor: pointer;
}

.server_bg {
  background-color: #3363ff;
  color: #ffffff;
}

.server_line1 {
  background: rgba(238, 238, 238, 1);
  width: 580px;
  height: 2px;
}

.search_menu {
  /*border: 1px solid red;*/
  margin-top: 16px;
  margin-left: 20px;
  width: 540px;
  height: calc(100vh - 202px);
  overflow: hidden;
}

.search_bor {
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 5px;
  background: rgba(246, 246, 246, 1);
  width: 538px;
  height: 50px;
}

.search_input {
  margin-top: 15px;
  margin-left: 25px;
  outline: none;
  border: 0;
  background-color: rgba(0, 0, 0, 0);
  width: 400px;
}

.search_bor img {
  margin-top: 11px;
  margin-left: 30px;
  width: 24px;
  height: 26px;
}

.search_bor img:hover {
  cursor: pointer;
}

.search_label {
  /*border: 1px red solid;*/
  float: left;
  width: 540px;
  overflow: hidden;
}

.search_label ul li {
  float: left;
  margin-top: 12px;
  margin-left: 10px;
  border: 1px solid #999999;
  border-radius: 22px;
  overflow: hidden;
}

.search_label ul li {
  cursor: pointer;
}

.search_label ul li p {
  display: inline-block;
  padding: 11px 32px 11px 32px;
  color: #666666;

  font-weight: 400;
  font-size: 17px;
}

.bg_label {
  border: 1px solid #3363ff !important;
  background: #3363ff;
}

/*swiper测试*/
/*.swiper-container {*/
/*width: 600px;*/
/*height: 300px;*/
/*}*/
.search_detail {
  margin-top: 16px;
  width: 540px;

  /*border: 1px solid red;*/
}

.search_detail .search_detail1 {
  cursor: pointer;
}

.search_detail .search_detail1 .serach_detail_info {
  display: flex;
  overflow: hidden;
}

.search_detail .search_detail1 .serach_detail_info img {
  display: inline-block;
  margin-top: 12px;
  /*border: 1px solid green;*/
  margin-left: 10px;
  width: 110px;
  height: 100px;
}

.search_detail .search_detail1 .serach_detail_info .serach_detail_info_font {
  margin-top: 12px;
  margin-left: 14px;
  height: 100px;
  /*border: 1px solid gold;*/
}

.search_detail
  .search_detail1
  .serach_detail_info
  .serach_detail_info_font
  .serach_detail_info_font1 {
  margin-top: 12px;
  color: rgba(51, 51, 51, 1);

  font-weight: 400;
  font-size: 20px;
}

.search_detail
  .search_detail1
  .serach_detail_info
  .serach_detail_info_font
  .serach_detail_info_font2 {
  /*border: 1px solid red;*/
  display: flex;
  margin-top: 32px;
  width: 91px;
  height: 18px;
  color: rgba(153, 153, 153, 1);

  font-weight: 400;
  font-size: 18px;
  /*overflow: hidden;*/
}

.search_detail .search_detail1 .search_detail_line {
  margin-top: 12px;
  border: 0.5px solid rgba(229, 229, 229, 1);
  width: 540px;
}

.menu_line {
  border: 1px solid rgba(238, 238, 238, 1);
  background: rgba(238, 238, 238, 1);
  width: 4px;
}

.open_details {
  flex: 1;
  height: calc(100vh - 60px);
  /*overflow: auto;*/
}

.open_details_title_font1 {
  display: inline-block;
  width: 120px;
  height: 32px;
  color: rgba(0, 0, 0, 1);
  font-weight: 400;
  font-size: 22px;
  text-align: center;
  /*border: 1px solid red;*/
}

.open_details_border {
  position: relative;
  box-sizing: border-box;
  border-bottom: 2px solid rgba(238, 238, 238, 1);
  height: 60px;
  line-height: 60px;
}

.open_details_title {
  font-weight: 400;
  font-size: 16px;
  text-align: center;
}

.open_details_title_font2 {
  position: absolute;
  top: 15px;
  right: 18px;
  cursor: pointer;
  border: 1px solid #3363ff;
  border-radius: 8px;
  padding: 2px 8px;
  height: 24px;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
}

.open_details_info {
  box-sizing: border-box;
  padding: 18px 20px;
  overflow: hidden;
}

.search_open {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  margin-bottom: 18px;
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 5px;
  background: rgba(246, 246, 246, 1);
  padding: 0 15px;
  height: 48px;
}

.search_opne_input {
  flex: 1;
  outline: none;
  border: 0;
  background-color: rgba(0, 0, 0, 0);
  height: 48px;
  color: rgba(187, 187, 187, 1);
  font-weight: 400;
  font-size: 14px;
}

.search_open img {
  display: inline-block;
  width: 24px;
  height: 26px;
}

.search_open img:hover {
  cursor: pointer;
}

.open_server_name {
}

.open_details_price {
  display: flex;
  align-items: center;
  padding-right: 15px;
}

.open_shop {
  flex: 1;
  box-sizing: border-box;
  box-sizing: border-box;
  border-left: 6px solid #3363ff;
  padding: 40px 15px;
}

.open_server_name .open_details_price {
  background: #f5f5f5;
  overflow: hidden;
  /*margin-top: 18px*/
}

.open_details_price_bg {
  background: #edf2fe;
}

.open_details_price_line {
  border: 1px solid #f5f5f5;
  background: #f5f5f5;
  width: 4px;
}

.open_details_price_line_bg {
  border: 1px solid #3363ff;
  background: #3363ff;
}

.font-weight {
  font-weight: 400;
}

.open_details_price_name,
.open_details_price_num,
.open_details_price_all {
  flex: 1;
}

.open_details_price_name {
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 20px;
}

.open_details_price_num {
  color: black;
  font-weight: 400;
  font-size: 18px;
}

.open_details_price_num img {
  margin-top: 4px;
  width: 18px;
  height: 18px;
}

.open_details_price_all {
  /*width:182px;*/
  height: 25px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 18px;
}

.open_details_price_all img {
  margin-top: 4px;
  width: 18px;
  height: 18px;
}

.open_details_price_del {
  margin-top: 30px;
  width: 24px;
  height: 24px;
}

.open_details_price_del > img {
  display: inline-block;
}

.open_details_price_del img:hover {
  cursor: pointer;
}

.change_all_price {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  border-left: 6px solid #3363ff;
  padding: 0 15px;
  height: 60px;
  overflow: hidden;
  line-height: 60px;
}

.change_all_price_line {
  border: 1px solid #3363ff;
  background: #3363ff;
  width: 4px;
}

.change_all_price_font1 {
  color: #999999;
}

.change_all_price_font2 {
  font-size: 14px;
}

.change_all_price_font2 img {
  margin-top: 1px;
  width: 15px;
  height: 15px;
  color: #333333 !important;
}

.change_all_price_font2 span {
}

.change_all_price_font3 {
  color: #999999;
  font-size: 14px;
}

.change_all_price_font3:hover {
  cursor: pointer;
}

.chioce_technician {
  display: flex;
  border-left: 6px solid #3363ff;
  height: 60px;
  overflow: hidden;
}

.chioce_technician_line {
  border: 1px solid #3363ff;
  background: #3363ff;
  width: 5px;
}

.chioce_technician_name,
.selective_sales_volume,
.batch {
  flex: 1;
}

.chioce_technician_name {
  border: 1px solid #f5f5f5;
  padding-left: 23px;
  height: 58px;
  line-height: 58px;
}

.selective_sales_volume {
  border: 1px solid #f5f5f5;
  padding-left: 23px;
  height: 58px;
  line-height: 58px;
}

.batch {
  border: 1px solid #f5f5f5;
  padding-left: 23px;
  height: 58px;
  line-height: 58px;
}

.chioce_technician_name_font1 {
  color: #3363ff;

  font-weight: 400;
  font-size: 14px;
}

.chioce_technician_name_font1:hover {
  cursor: pointer;
}

.chioce_technician_name_font2 {
  margin-left: 23px;
  color: rgba(51, 51, 51, 1);

  font-weight: 400;
  font-size: 14px;
}

.selective_sales_volume_font1 {
  color: #3363ff;

  font-weight: 400;
  font-size: 14px;
}

.selective_sales_volume_font1:hover {
  cursor: pointer;
}

.selective_sales_volume_font2 {
  margin-left: 23px;
  color: rgba(51, 51, 51, 1);

  font-weight: 400;
  font-size: 14px;
}

.batch_font1 {
  cursor: pointer;
  color: #3363ff;

  font-weight: 400;
  font-size: 14px;
}

.batch_font2 {
  margin-left: 23px;
  color: rgba(51, 51, 51, 1);

  font-weight: 400;
  font-size: 14px;
}

.order_remark {
  box-sizing: border-box;
  padding: 0 18px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 18px;
}

.remark_input {
  outline: none;
  border: 0;
  background-color: rgba(0, 0, 0, 0);
  color: rgba(187, 187, 187, 1);
  font-weight: 400;
  font-size: 14px !important;
}

.order_remark_font {
  height: 50px;
  font-size: 14px;
  line-height: 50px;
}

.order_remark_input {
  box-sizing: border-box;
  border-top: 1px solid #e5e5e5;
  border-right: 1px solid #e5e5e5;
  border-left: 1px solid #e5e5e5;
  padding: 0 15px;
  height: 59px;
}

.remark_input {
  width: 100%;
  height: 100%;
  font-size: 18px;
}

.open_details_pay {
}

.chioce_Discount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  border: 1px solid #e5e5e5;
  padding-left: 15px;
  height: 58px;
}

.chioce_Discount_font1 {
  height: 23px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 18px;
}

.chioce_Discount_font0 {
  font-size: 14px;
}

.chioce_Discount_font2 {
  width: 60px;
  height: 19px;
  color: #3363ff;
  font-weight: 400;
  font-size: 20px;
}

.chioce_Discount_font3 {
  width: 20px;
  height: 20px;
}

.open_details_pay_choice {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  border: 1px solid #e5e5e5;
  padding-left: 15px;
}

.open_details_pay_choice_font4,
.open_details_pay_choice_font2,
.open_details_pay_choice_font3 {
  cursor: pointer;
  box-sizing: border-box;
  padding: 20px 50px;
  color: rgba(255, 255, 255, 1);
  font-weight: bold;
  font-size: 20px;
}

.open_details_pay_choice_font1 {
  color: rgba(51, 51, 51, 1);
  font-size: 20px;
}

.open_details_pay_choice_font2 {
  background: rgba(122, 122, 122, 1);
}

.open_details_pay_choice_font3 {
  background: rgba(43, 40, 44, 1);
}

.open_details_pay_choice_font4 {
  background: #3363ff;
}

.order_three {
  display: flex;
}

/*模态框 选择技师*/
.xuanze_jishi {
  margin-bottom: 24px;
  padding-bottom: 24px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 21px;
  text-align: center;
}

.over_save {
  display: flex;
  cursor: pointer;
}

.over_save_over {
  flex: 1;
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 0px 0px 0px 7px;
  padding: 18px 0px 18px 0px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 21px;
  text-align: center;
}

.over_save_save {
  flex: 1;
  border-radius: 0px 0px 7px 0px;
  background: #3363ff;
  padding: 18px 0px 18px 0px;
  color: rgba(255, 255, 255, 1);
  font-weight: 400;
  font-size: 21px;
  text-align: center;
}

.xuazne_xiaoshou {
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.qudan_font {
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 21px;
}

.detail_say {
  border-radius: 4px;
  background: rgba(246, 246, 246, 1);
  padding: 10px 30px 10px 30px;
}

.detail_ul {
  display: flex;
  justify-content: space-between;
}

.detail_ul li {
  color: rgba(102, 102, 102, 1);
  font-weight: 400;
  font-size: 17px;
}

.qu_dan {
  padding-bottom: 48px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 22px;
  text-align: center;
}

.shops {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(238, 238, 238, 1);
}

.shops_num {
  /*flex:1;*/
  padding: 12px 0px 12px 30px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 13px;
}

.server_name {
  /*flex:1;*/
  padding: 21px 0px 21px 50px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 13px;
  line-height: 38px;
  text-align: center;
}

.shops_name {
  /*flex:1;*/
  padding: 21px 0px 21px 20px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 13px;
}

.shops_name_font {
  padding: 5px 0px 0px 0px;
}

.shops_servername {
  /*flex:1;*/
  padding: 21px 30px 21px 0px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 13px;
}

.shops_servername_font {
  padding: 5px 0px 0px 0px;
}

.shops_price {
  /*flex:1;*/
  padding: 21px 0px 21px 0px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 13px;
  line-height: 38px;
  text-align: center;
}

.shops_get_money {
  cursor: pointer;
  /*flex: 1;*/
  margin: 15px 5px 15px 0px;
  border-radius: 4px;
  background: #3363ff;
  padding: 10px 17px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 13px;
  line-height: 38px;
  text-align: center;
}

/*以下是会员的样式*/
/*添加会员 ， */
.huiyaun_add_huiyuan {
  margin: 0 auto;
  background-color: rgb(253, 253, 253);
  /* padding-left: 30px; */
  width: 95%;
}

.border-bottom {
  /* border-bottom: 7px solid #F6F6F6; */
  width: 60%;
}

.padding20 {
  box-sizing: border-box;
  padding: 0 20px;
}

.huiyaun_add_huiyuan_main {
  /* padding-top: 10px; */
  border: 1px solid white;
}

.vip-main-form {
  max-height: calc(100vh - 100px);
  overflow: auto;
}

.vip-main-form::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.vip-main-form::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.vip-main-form::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.huiyuan_info_font {
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 14px;
}

.huiyaun_info_one {
  display: flex;
  align-items: center;
  /* border-bottom: 1px solid #E5E5E5; */
  padding: 5px 0px;
}

.huiyuan_info_font {
  width: 80px;
}

.huiyuan_info_font1 {
  flex: 1;
}

.huiyuan_info_font8 {
  width: 300px;
  font-size: 14px;
  white-space: nowrap;
  /*width: 20%;*/
}

.member_input input {
  width: 350px;
}

.huiyuan_name_input::-webkit-input-placeholder {
  /* WebKit browsers */
  color: rgba(187, 187, 187, 1);
}

.huiyuan_name_input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: rgba(187, 187, 187, 1);
}

.huiyuan_name_input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: rgba(187, 187, 187, 1);
}

.huiyuan_name_input:-ms-input-placeholder {
  /* Internet Explorer 10+ */
  color: rgba(187, 187, 187, 1);
}

.huiyuan_info_font2 {
  flex: 1;
  cursor: pointer;
  padding: 5px 0px 0px 0px;
}

.huiyuan_info_font3 {
  flex: 1;
  cursor: pointer;
  padding: 5px 0px 0px 0px;
}

.huiyaun_info_one1 {
  display: flex;
  border-bottom: 7px solid #e5e5e5;
  padding: 15px 0px;
}

.huiyuan_info_font4 {
  flex: 1;

  /*cursor: pointer;*/
}

.huiyuan_info_font5 {
  display: flex;
  flex: 1;
  padding: 1px 0px 0px 0px;
}

.vip_sex {
  cursor: pointer;
  border: 1px solid rgba(229, 229, 229, 1);
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  padding: 5px 10px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px !important;
}

.vip_sex_show {
  border: 1px solid #3363ff;
  background: #3363ff;
  color: white;
}

.vip_sex_none {
}

.huiyaun_info_one2 {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 10px 0px;
}

.huiyuan_info_font6 {
  flex: 1;
}

.huiyuan_info_font7 {
  padding: 12px 0px 0px 0px;
  width: 80px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 14px;
}

.huiyaun_info_one3 {
  display: flex;
  /* border-bottom: 1px solid #E5E5E5; */
  padding: 5px 0px;
}

.huiyaun_info_one4 {
  /*padding: 80px;*/
  box-sizing: border-box;
  width: 60%;
  text-align: center;
  white-space: nowrap;
}

.huiyuan_info_anniu {
  display: flex;
  justify-content: space-between;
  margin: 0 auto;
  padding-top: 15px;
  width: 250px;
}

.huiyuan_info_font10 {
  cursor: pointer;
  border: 1px solid #3363ff;
  border-radius: 4px;
  padding: 12px 40px;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.huiyuan_info_font9 {
  cursor: pointer;
  border: 1px solid #3363ff;
  border-radius: 4px;
  background: #3363ff;
  padding: 12px 40px;
  color: rgba(255, 255, 255, 1);
  font-weight: 400;
  font-size: 14px;
}

.vip_save_show {
  background: #3363ff;
  color: rgba(255, 255, 255, 1);
}

.vip_save_none {
  background: rgba(255, 255, 255, 1);
  color: #3363ff;
}

/*以下是查看会员的css*/

.hy_chakan_kaidan {
  position: relative;
  border-bottom: 1px solid rgba(238, 238, 238, 1);
  width: 100%;
  height: 60px;
}

.hy_chakan_kaidan_font0 {
  width: 100%;
  line-height: 60px;
  text-align: center;
}

.hy_chakan_kaidan_font1 {
  position: absolute;
  top: 14px;
  right: 15px;
  cursor: pointer;
  box-sizing: border-box;
  border: 1px solid #3363ff;
  border-radius: 4px;
  padding: 8px 20px;
  color: #3363ff;
  font-size: 14px;
}

.hy_chakan_kandan_sousuo {
  box-sizing: border-box;
  padding: 15px 40px;
  width: 100%;
}

.hy_info_touxiang {
  width: 60px;
  height: 60px;
}

.hy_chakan_table {
  padding: 0 40px;
}

.member_name {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.membershipDetail_box {
  margin-top: 7vh;
}

.vipTable {
  margin-bottom: 15px;
}

.vipTable .el-table__body-wrapper {
  /* 120 70 32 48  15*/
  height: calc(100vh - 270px);
  overflow-y: auto;
}

.vipTable .el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.vipTable .el-table__body-wrapper::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.vipTable .el-table__body-wrapper::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.dialog-title {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  border-bottom: 1px solid #ebeef5;
  padding: 15px 25px;
}

.del_shaixuan {
  cursor: pointer;
  color: #3363ff;
  font-weight: 400;
  font-size: 16px;
}

.shaixuan_titel {
  color: black;
  font-weight: 400;
  font-size: 16px;
}

.shaixuan_wancheng {
  cursor: pointer;
  color: #3363ff;
  font-weight: 400;
  font-size: 16px;
}

.memberScreening {
  height: calc(100vh - 300px);
  overflow: auto;
}

.memberScreening::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}
.memberScreening::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.memberScreening::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #f6f6f6;
}

.memberScreening .el-collapse {
  box-sizing: border-box;
  padding: 10px 25px;
}

.screening-list {
}

.list-tabel {
  box-sizing: border-box;
  border-bottom: 5px solid #f6f6f6;
  padding: 15px 25px;
}

.list-tabel-title {
  margin-bottom: 15px;
}

.hy_shaixuan1 {
  display: flex;
  flex-wrap: wrap;
  box-sizing: border-box;
  padding-bottom: 15px;
}

.hy_shaixuan1_font {
  cursor: pointer;
  box-sizing: border-box;
  margin-right: 10px;
  margin-bottom: 10px;
  border: 1px solid #999999;
  border-radius: 4px;
  padding: 8px 12px;
  color: #666666;
  font-size: 14px;
}

.memberScreening .el-collapse-item__content {
  padding-bottom: 0;
  line-height: 1;
}

.shaixuan1_add {
  border: 1px solid #3363ff;
  background: #3363ff;
  color: white;
}

.element-input {
  margin-right: 15px;
}

.el-input.hy_shaixuan1_input {
  width: 150px;
  color: #666666;
}

.hy_model1_title {
  display: flex;
  padding: 0px 30px !important;
  height: 60px;
  color: rgba(43, 40, 44, 1);
  /*justify-content: space-between;*/
  font-size: 16px;
  line-height: 60px;
}

.el-dialog__header {
  padding: 0px 0px 0px 0px !important;
}

.vip_model1_imgs {
  border-radius: 50%;
  width: 80px;
  height: 80px;
}

.vip_model1_info1 {
  padding-left: 20px;
}

.vip_model1_info1_font1 {
  padding-bottom: 5px;
  width: 150px;
  overflow: hidden;
  color: #333333;
  font-weight: 400;
  font-size: 16px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vip_model1_info1_font2 {
  color: #333333;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_info1_font3 {
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.el-dialog__body {
  padding: 0px 0px !important;
}

.vip_model1_info2_font1 {
  padding: 55px 10px 0px 0px;
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_info2_font2 {
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_info3_font1 {
  padding: 47px 10px 0px 0px;
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_info4_font4 {
  display: flex;
  /* justify-content: space-between; */
  padding-bottom: 10px;
}

.vip_model1_info4_font5 {
  display: flex;
  /* justify-content: space-between; */
  /* padding-top: 10px; */
}

.vip_model1_info4_font1 {
  cursor: pointer;
  margin-right: 10px;
  border: 1px solid #3363ff;
  border-radius: 5px;
  background: #3363ff;
  padding: 5px 15px;
  color: rgba(255, 255, 255, 1);
}

.vip_model1_info4_font2 {
  cursor: pointer;
  margin-right: 10px;
  border: 1px solid #3363ff;
  border-radius: 5px;
  background: #3363ff;
  padding: 5px 10px;
  color: rgba(255, 255, 255, 1);
}

.vip_model1_info4_font3 {
  cursor: pointer;
  margin-right: 10px;
  border: 1px solid #333333;
  border-radius: 5px;
  background: #333333;
  padding: 5px 10px;
  color: #e2c492;
}

.vip_model1_info5_font1 {
  display: flex;
  justify-content: space-around;
  margin: 15px 0;
  background: #f6f6f6;
  padding: 5px 0px;
  width: 100%;
}

.vip_model1_info5_font2 {
  display: flex;
  justify-content: space-between;
  margin: 5px 0px;
}

.vip_model1_info5_font3 {
}

.vip_model1_info5_font4 {
  padding: 5px 0px;
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_info5_font5 {
  padding: 5px 0px;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
  text-align: center;
}

.vip_model1_info5_font6 {
  margin: 5px 10px;
  border: 0.5px solid rgba(229, 229, 229, 1);
  height: 40px;
}

.vip_model1_info5_font7 {
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_info5_font8 {
  padding: 5px 0px;
}

.vip_model1_info5_font9 {
  padding: 5px 0px;
}

.vip_model1_info5_font10 {
  transition-duration: 150ms;
  transition-property:
    color, background-color, border-color, text-decoration-color, fill, stroke,
    opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  padding: 20px 10px;
  width: 185px;
  text-align: center;
}

.vip_model1_info5_font10:hover {
  background: #fff;
}

.vip_model1_info5_font11 {
  padding: 5px 0px;
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_info5_font12 {
  padding: 5px 0px;
  font-weight: 700;
  font-size: 18px;
  text-align: center;
}

.vip_model1_info5_font13 {
  margin: 5px 0px;
  padding: 5px 0px 5px 10px;
  width: 165px !important;
}

.vip_model1_info5_font14 {
  padding: 5px 0px 5px 0;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_caozuo {
  margin: auto;
  width: 100%;
}

.vip_model1_caozuo .el-tabs__item {
  /* font-size: 20px !important; */
}

.vip_model1_vip_title_font1 {
  display: flex;
  justify-content: space-between;
  border-left: 4px solid rgba(204, 204, 204, 1);
  width: 100%;
}

.vip_model1_vip_title_font2 {
  padding-left: 10px;
  color: #333333;
  font-weight: 400;
  font-size: 16px;
}

.vip_model1_vip_title_font3 {
  cursor: pointer;
  padding-right: 30px;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_vip_title_font4 {
  margin-right: 10px;
  border: 1px solid #3363ff;
  border-radius: 4px;
  padding: 8px 12px;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_vip_title_font5 {
  display: flex;
  margin: 20px 0px 10px 5px;
}

.vip_model1_vip_title_font6 {
  display: flex;
  justify-content: space-between;
  margin: 20px 0px 0px 0px;
}

@media screen and (max-width: 1200px) {
  .vip_model1_vip_title_font7 {
    flex: 0.45;
    /* height: 110px; */
    overflow: hidden;
    overflow-y: auto;
  }
  .trade_recond {
    /* height: 170px; */
    overflow: hidden;
    overflow-y: auto;
  }
}

@media screen and (min-width: 1201px) {
  .vip_model1_vip_title_font7 {
    flex: 0.45;
    overflow: hidden;
    overflow-y: auto;
  }
  .trade_recond .el-table__body-wrapper {
    overflow: hidden;
    overflow-y: auto;
    /* height: 440px; */
  }
}

.vip_model1_vip_title_font7::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.vip_model1_vip_title_font7::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.vip_model1_vip_title_font7::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.trade_recond .el-table__body-wrapper::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.trade_recond .el-table__body-wrapper::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.trade_recond
  .el-table__body-wrapper::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

/*交易记录的滚动条*/
.trade_recond::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.trade_recond::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.trade_recond::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

/*服务日志内容*/
.service_log_content {
  padding-left: 20px;
}

.service_log_img {
  margin-top: 14px;
  margin-left: 20px;
}

.service_log_img img {
  margin-right: 5px;
  width: 50px;
  height: 50px;
}

.vip_model1_vip_title_font8 {
  display: flex;
  justify-content: space-between;
  border-left: 4px solid rgba(204, 204, 204, 1);
  width: 100%;
}

.vip_model1_vip_title_font9 {
  padding-left: 10px;
  color: #333333;
  font-weight: 400;
  font-size: 16px;
}

.vip_model1_vip_title_font10 {
  cursor: pointer;
  padding-right: 30px;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_vip_title_font11 {
  display: flex;
  margin: 20px 0px 0px 0px;
  border-top: 1px solid rgba(229, 229, 229, 1);
  padding-top: 20px;
}

.vip_model1_vip_title_font12 {
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_vip_title_font13 {
  padding-bottom: 10px;
}

.vip_model1_vip_title_font14 {
  padding-left: 30px;
  width: 65%;
  color: #333333;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_vip_title_font15 {
  padding-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.vip_model1_vip_title_font16 {
  flex: 0.45;
}

.vip_model1_vip_title_font17 {
  display: flex;
  justify-content: space-between;
  border-left: 4px solid rgba(204, 204, 204, 1);
  width: 100%;
}

.vip_model1_vip_title_font18 {
  padding-left: 10px;
  color: #333333;
  font-weight: 400;
  font-size: 16px;
}

.vip_model1_vip_title_font19 {
  cursor: pointer;
  padding-right: 30px;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_vip_title_font20 {
  margin: 20px 0px 0px 0px;
  border-top: 1px solid rgba(229, 229, 229, 1);
  padding-top: 20px;
}

.vip_model1_vip_title_font21 {
  display: flex;
}

.vip_model1_vip_title_font22 {
  padding-right: 20px;
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_vip_title_font23 {
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_vip_title_font24 {
  padding: 20px 0px 0px 0px;
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_vip_title_font26 {
  padding: 10px 0px 0px 0px;
}

.vip_model1_vip_title_font27 {
  padding: 10px;
  width: 50px;
  height: 50px;
}

.vip_model1_info_fuwu_hight {
  height: calc(100vh - 510px);
  overflow: auto;
}

.vip_model1_info_fuwu_hight::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.vip_model1_info_fuwu_hight::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.vip_model1_info_fuwu_hight::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.vip_model1_info_fuwu {
  box-sizing: border-box;
  margin-bottom: 20px;
  border: 1px solid rgba(229, 229, 229, 1);
  width: 100%;
}

.vip_model1_info_fuwu_font1 {
  display: flex;
  justify-content: space-between;
  margin: 10px 10px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.vip_model1_info_fuwu_font2 {
  display: flex;
}

.vip_model1_info_fuwu_font3 {
  width: 50px;
  height: 50px;
}

.vip_model1_info_fuwu_font4 {
  padding: 5px 10px;
}

.vip_model1_info_fuwu_font5 {
  margin-bottom: 10px;
  color: black;
  font-weight: 400;
  font-size: 16px;
}

.vip_model1_info_fuwu_font6 {
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_info_fuwu_font7 {
  padding: 20px 10px;
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model1_info_fuwu_font8 {
  margin: 10px 10px;
  padding-top: 30px;
}

.vip_model1_info_fuwu_font9 {
  background: #999999;
  padding-left: 10px;
  height: 40px;
  color: black;
  font-weight: 400;
  font-size: 14px;
  line-height: 40px;
}

/*以下是付费会员模态框*/

.saoma_chongzhi {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 400px;
  height: 60px;
}

.bt_saoma {
  cursor: pointer;
  border: 1px solid #3363ff;
  border-radius: 5px;
  padding: 0px 8px 0px 8px;
  height: 40px;
  color: #3363ff;
  line-height: 40px;
}

.chongzhi_view {
  color: #3363ff;
  font-weight: 400;
  font-size: 16px;
}

.chonngzhi_vip_all {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e5e5e5;
  padding: 15px 20px;
}

.chongzhi_vip {
  display: flex;
  flex: 0.5;
}

.chongzhi_vip_info {
  width: 100px;
  height: 100px;
}

.chongzhi_vip_info1 {
  padding: 10px 0px 10px 40px;
}

.chongzhi_vip_info_font1 {
  padding-bottom: 15px;
  color: rgba(51, 51, 51, 1);
  font-weight: bold;
  font-size: 16px;
}

.chongzhi_vip_info_font2 {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  width: 200px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.chongzhika_info {
}

.chongzhi_vip_info_font3 {
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.chongzhi_vip_info_font4 {
  flex: 0.5;
  cursor: pointer;
  padding-right: 3px;
  font-size: 20px !important;
  line-height: 90px;
  text-align: right !important;
}

.chaongzhika_name_money {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e5e5e5;
}

.chongzhika_name_font {
  padding: 30px 0px 30px 24px;
  width: 150px;
  color: rgba(51, 51, 51, 1);
  font-weight: bold;
  font-size: 16px;
}

.chongzhika_money_font0 {
  display: flex;
  width: 250px;
}

.chongzhika_money_font1 {
  flex: 0.45;
  padding: 46px 0px 0px 0px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.chongzhika_money_font2 {
  flex: 0.45;
  padding: 20px 0px 0px 0px;
}

.chongzhika_money_font3 {
  padding-bottom: 10px;
  color: #3363ff;
  font-weight: 400;
  font-size: 16px;
}

.chongzhika_money_font4 {
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.chongzhika_money_font5 {
  flex: 0.1;
  cursor: pointer;
  font-size: 28px !important;
  line-height: 80px;
}

.chioce_paynum {
  cursor: pointer;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding: 15px 0px 15px 25px;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.chongzhi_pay_num {
  display: flex;
  justify-content: space-between;
  border-bottom: 0.5px solid #e5e5e5;
  padding: 15px 0px 15px 25px;
}

.chioce_paynum_font1 {
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.chioce_paynum_font2 {
  cursor: pointer;
  outline: none;
  border: 0;
  background: 0;
  color: #3363ff !important;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 14px;
}

.vip_pay_cengson {
  border-bottom: 2px solid #e5e5e5;
  padding: 15px 0px 15px 25px;
}

.vip_pay_cengson_font1 {
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.vip_pay_cengson_font2 {
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 14px;
}

.vip_xuanze_xiaoshou {
  border-bottom: 0.5px solid #e5e5e5;
  padding: 15px 0px 15px 25px;
}

.vip_xuanze_xiaoshou1 {
  cursor: pointer;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.vip_xuanze_xiaoshou2 {
  cursor: pointer;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.vip_xuanze_xiaoshou3 {
}

.vip_add_zengson {
  cursor: pointer;
  border-bottom: 0.5px solid #e5e5e5;
  padding: 15px 0px 15px 25px;
}

.add_zengson {
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

/*收款的样式*/

.chongzhi_vip_info_img {
  width: 100px;
  height: 100px;
}

.chongzhi_shoukuan {
}

.chongzhi_shoukuan_font {
  flex: 1;
  cursor: pointer;
  background: #3363ff;
  padding: 20px 0px;
  width: 200px;
  color: rgba(255, 255, 255, 1);
  font-weight: bold;
  font-size: 18px;
  text-align: center;
}

/*支付二维码*/

.saoma_weweima {
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding-bottom: 10px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 21px;
  text-align: center;
}

.zhifu_weweima {
  text-align: center;
}

.zhifu_weweima_img {
  width: 400px;
  height: 400px;
}

/*选择充值金额*/

.chongzhi_chongzhi_jine {
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding-bottom: 24px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 21px;
  text-align: center;
}

.chongzhi_jine_mian {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  height: calc(100vh - 400px);
}

.chongzhi_danka {
  margin-bottom: 10px;
  margin-left: 10px;
  /*padding: 30px 80px 30px 30px;*/
  border: 1px solid rgba(229, 229, 229, 1);
  width: 180px;
  height: 100px;
}

.danka_get {
  padding: 25px 0px 5px 10px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 24px;
}

.danka_post {
  padding: 0px 0px 0px 10px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 18px;
}

/*充值页面添加服务样式*/

.tianjia_fuwu {
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 22px;
  text-align: center;
}

.tianjia_fuwu_search {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 4px;
  padding: 10px 5px 10px 5px;
}

.tianjia_fuwu_input {
  outline: none;
  border: 0;
  background: 0;
}

.tianjia_fuwu_mian {
  display: flex;
  margin-top: 10px;
  height: calc(100vh - 600px);
}

.fuwu_biaoti {
  width: 20%;
}

.fuwu_biaoti_chioce {
  width: 79%;
}

.tianjia_fuwu_font {
  cursor: pointer;
  border-bottom: 0.5px solid rgba(221, 221, 221, 1);
  padding: 10px 10px 10px 10px;
  color: rgba(102, 102, 102, 1);
  font-weight: 400;
  font-size: 14px;
}

.tianjia_fuwu_font0 {
  cursor: pointer;
  border-bottom: 0.5px solid rgba(221, 221, 221, 1);
  background: #ebdcf2;
  padding: 10px 10px 10px 10px;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.fuwu_biaoti_line {
  /*width: %;*/
  border: 0.5px solid rgba(221, 221, 221, 1);
}

.fuwu_biaoti_chioce {
}

.fuwu_biaoti_chioce_bottom {
  border-bottom: 0.5px solid rgba(221, 221, 221, 1);
}

.server_biaoti_name_font {
  display: flex;
  justify-content: space-between;
  padding: 10px 0px 10px 0px;
  height: 30px;
  line-height: 30px;
}

.server_biaoti_name_font1 {
  margin-top: -3px;
  width: 40px;
  height: 40px;
}

.server_biaoti_name_font2 {
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 16px;
}

.server_biaoti_name_font3 {
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 16px;
  text-align: right;
}

.cz_open_details_info {
  padding: 20px 25px;
}

.hy_open_details_num {
  margin-left: 20px;
  height: 50px;
  color: black;
  font-weight: 400;
  font-size: 14px;
  line-height: 70px;
}

.hy_span1 {
  cursor: pointer;
  border: 1px solid rgba(229, 229, 229, 1);
  background: rgba(246, 246, 246, 1);
  padding: 4px 10px 4px 10px;
}

.hy_span2 {
  border: 1px solid rgba(229, 229, 229, 1);
  border-right: none;
  border-left: none;
  padding: 4px 10px 4px 10px;
}

.hy_span3 {
  cursor: pointer;
  border: 1px solid rgba(229, 229, 229, 1);
  background: rgba(246, 246, 246, 1);
  padding: 4px 8px 4px 8px;
}

.hy_span4 {
}

.hy_model2_fufeihuiyuan {
}

.hy_model2_fufei_font1 {
  display: flex;
  justify-content: space-between;
  border-bottom: 0.5px solid rgba(229, 229, 229, 1);
  background: rgba(249, 249, 249, 1);
  padding: 20px 25px;
}

.hy_model2_fufei_font2 {
}

.hy_model2_fufei_font4 {
  padding-bottom: 8px;
  color: #333333;
  font-weight: 400;
  font-size: 16px;
}

.hy_model2_fufei_font5 {
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.hy_model2_fufei_font3 {
}

.hy_model2_fufei_font6 {
  padding-bottom: 15px;
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.hy_model2_fufei_font_lj {
  cursor: pointer;
  color: #999999;
  font-weight: 400;
  font-size: 20px !important;
  text-align: right;
}

.hy_model2_fufei_font7 {
  display: flex;
  justify-content: space-between;
  border-bottom: 0.5px solid rgba(229, 229, 229, 1);
  padding: 20px 25px;
}

.hy_model2_fufei_font8 {
  min-width: 90px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 14px;
}

.vip-model2_server_name {
}

.vip-model2_server_name_font1 {
  padding: 15px 25px;
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip-model2_server_name_font2 {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding: 0px 25px;
  height: calc(100vh - 831px);
  overflow: auto;
}

.vip-model2_server_name_font3 {
  color: #333333;
  font-weight: 400;
  font-size: 14px;
  line-height: 70px;
}

.vip-model2_server_name_font4 {
  line-height: 70px;
}

.vip-model2_server_name_font5 {
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.vip-model2_server_name_font6 {
  color: #333333;
  font-weight: 400;
  font-size: 14px;
}

.vip-model2_server_name_font7 {
  color: #999999 !important;
  font-weight: 400 !important;
  font-size: 14px !important;
}

.vip_model2_left {
  padding: 20px 20px;
  height: calc(100vh - 60px);
}

.vip_model2_top {
  background: url("../images/huiyuanzhuanxiangka_bg.png");
  background-size: cover;
  width: 460px;
  height: 247px;
}

.vip_model2_top_font1 {
  padding-top: 90px;
  color: rgba(245, 212, 156, 1);
  font-weight: 400;
  font-size: 18px;
  text-align: center;
}

.vip_model2_top_font2 {
  padding-top: 20px;
  color: rgba(245, 212, 156, 1);
  font-weight: 400;
  font-size: 16px;
  text-align: center;
}

.vip_model2_mid {
  display: flex;
  justify-content: space-between;
  padding: 40px 60px;
}

.vip_model2_mid1 {
  text-align: center;
}

.vip_model2_mid1_font1 {
  padding-bottom: 20px;
  width: 40px;
  height: 40px;
}

.vip_model2_mid1_font2 {
  color: rgba(102, 102, 102, 1);
  font-weight: 400;
  font-size: 14px;
}

.vip_model2_but {
  display: flex;
  justify-content: space-between;
  padding: 40px 60px;
}

.vip_model2_foot {
  padding: 40px 60px;
}

.vip_model2_foot1 {
  width: 70px;
  text-align: center;
}

/*model2的优惠权益*/
.xuanze_qunayi_font0 {
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding: 20px 0px 20px 30px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 14px;
}

.xuanze_qunayi_font1 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 200px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
}

.xuanze_qunayi_font2 {
  padding: 20px 0px 20px 30px;
  color: rgba(51, 51, 51, 1);
  font-weight: 400;
  font-size: 14px;
}

.xuanze_qunayi_font3 {
  padding: 20px 20px 20px 30px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  font-size: 14px;
}

.hy_model2_user_qianyi {
  padding: 20px 20px 10px !important;
}

.xuanze_jishi_search i {
  font-size: 18px;
}

.hy_model1_Editing_info {
  /*text-align: center;*/
  padding-left: 30px;
  height: 60px;
  font-weight: 500;
  font-size: 18px;
  line-height: 60px;
}

.vip_info1 {
  display: flex;
  border-bottom: 1px solid #ebebeb;
  padding: 20px 20px;
}

.vip_info2 {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #ebebeb;
  padding: 20px 20px;
}

.vip_info1_font1 {
  padding-top: 5px;
  min-width: 60px;
  color: black;
  font-weight: 400;
  font-size: 14px;
}

.vip_info1_font2 {
  margin-left: 40px;
  outline: none;
  border: 0;
  background: 0;
  width: 100%;
}

.vip_info1_font3 {
  margin-left: 40px;
  outline: none;
  border: 0;
  background: 0;
  width: 100%;
}

.vip_info1_font4 {
  margin-left: 40px;
  outline: none;
  border: 0;
  background: 0;
  width: 100%;
}

.vip_info1_same1::-webkit-input-placeholder {
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

/* Mozilla Firefox 4 to 18 */
.vip_info1_same1:-moz-placeholder {
  opacity: 1;
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

/* Mozilla Firefox 19+ */
.vip_info1_same1::-moz-placeholder {
  opacity: 1;
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

/* Internet Explorer 10+ */
.vip_info1_same1:-ms-input-placeholder {
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_info1_font5 {
  padding-top: 12px;
  min-width: 60px;
  color: black;
  font-weight: 400;
  font-size: 14px;
}

.vip_info3 {
  display: flex;
  border-bottom: 1px solid #ebebeb;
  padding: 10px 20px;
}

.vip_info3_font1 {
  margin-left: 40px;
}

.vip_info4 {
  display: flex;
  border-top: 1px solid #ebebeb;
  border-bottom: 1px solid #ebebeb;
  padding: 10px 20px;
}

.vip_info4_font1 {
  margin-left: 40px;
}

.vip_info1_font6 {
  box-sizing: border-box;
  margin-left: 40px;
  outline: none;
  border: 0;
  background: 0;
  width: 100%;
}

.vip_info1_font7 {
  cursor: pointer;
  background: #f6f6f6;
  padding: 2px 5px;
  color: #666666;
}

.vip_info1_font8 {
  cursor: pointer;
  margin-left: 10px;
  background: #f6f6f6;
  padding: 2px 5px;
  color: #666666;
}

.model1_sex_class {
  background: #3363ff !important;
  color: white !important;
}

.vip_model1_bangding_class {
  padding-left: 20px;
  height: 60px;
  line-height: 60px;
}

.hy_model1_shitika {
  padding: 10px 60px 0px 60px;
}

.hy_model1_shitika1 {
  display: flex;
  padding: 20px 0px;
}

.hy_model1_shitika1_font1 {
  width: 100px;
  color: black;
  font-weight: 400;
  font-size: 14px;
  line-height: 40px;
}

.hy_model1_shitika1_font2 {
  margin-left: 10px;
  width: 100%;
}

.hy_model1_shitika2 {
  display: flex;
  padding: 20px 0px;
}

.hy_model1_shitika2_font1 {
  width: 100px;
  color: black;
  font-weight: 400;
  font-size: 14px;
  line-height: 40px;
}

.hy_model1_shitika2_font2 {
  margin-left: 10px;
  width: 100%;
}

/*会员详情里面的开单页面css有的样式复用了*/

.hy_model1_title_cz {
  display: flex;
  justify-content: space-between;
  background: #fff;
  padding: 0px 30px !important;
  height: 60px;
  font-size: 18px;
  line-height: 60px;
}

.iconiconfontyoujiantou {
  font-weight: 400;
  font-size: 16px;
}

.hy_model1_cz_top1 {
  color: #000000;
  font-weight: 400;
  font-size: 16px;
}

.vip_model3_left1 {
  background: url("../images/chongzhika1_bg.png") no-repeat;
  background-size: cover;
  width: 460px;
  height: 92px;
}

.vip_model3_left1_fon1 {
  padding: 20px 20px;
  color: #ffffff;
  font-weight: 400;
  font-size: 16px;
}

.vip_model3_left1_fon2 {
  padding: 10px 20px;
  color: #ffffff;
  font-weight: 400;
  font-size: 12px;
}

.vip_model3_left2 {
  margin: 20px 0px;
  border-left: 4px solid #3363ff;
  padding-left: 10px;
  color: #333333;
  font-weight: 400;
  font-size: 14px;
}

.vip_model3_left3 {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e5e5e5;
  padding: 20px 10px;
}

.vip_model3_left3_font1 {
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model3_left3_font2 {
  color: #333333;
  font-weight: 400;
  font-size: 14px;
}

.vip_model3_left4 {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e5e5e5;
  padding: 20px 10px;
}

.vip_model3_left4_font1 {
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model3_left4_font2 {
  color: #333333;
  font-weight: 400;
  font-size: 14px;
}

.vip_model3_left5 {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e5e5e5;
  padding: 20px 10px;
}

.vip_model3_left5_font1 {
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model3_left5_font2 {
  color: #333333;
  font-weight: 400;
  font-size: 14px;
}

.vip_model3_left6 {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e5e5e5;
  padding: 20px 10px;
}

.vip_model3_left6_font1 {
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model3_left6_font2 {
  color: #333333;
  font-weight: 400;
  font-size: 14px;
}

.vip_model3_left7 {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e5e5e5;
  padding: 20px 10px;
}

.vip_model3_left7_font1 {
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model3_left7_font2 {
  color: #333333;
  font-weight: 400;
  font-size: 14px;
}

.vip_model3_left8 {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e5e5e5;
  padding: 20px 10px;
}

.vip_model3_left8_font1 {
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

.vip_model3_left8_font2 {
  color: #333333;
  font-weight: 400;
  font-size: 14px;
}

.vip_model3_left9 {
  border-bottom: 1px solid #e5e5e5;
  padding: 20px 10px;
  color: #333333;
  font-weight: 400;
  font-size: 14px;
}

.vip_model3_left10 {
  padding: 20px 10px;
  color: #999999;
  font-weight: 400;
  font-size: 14px;
}

/*会员model1的会员卡点击后页面css*/

.hy_model1_huiyuanka {
  padding: 20px 30px;
  width: 100%;
}

.hy_model4_top {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
  color: #3363ff;
  font-weight: 400;
  font-size: 16px;
}

.hy_model4_mid::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.hy_model4_mid::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.hy_model4_mid::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.hy_model4_mid {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 10px;
  box-sizing: border-box;
  padding: 20px;
  max-height: calc(100vh - 180px);
  overflow-y: auto;
}

.hy_model4_top_font1 {
  border: 1px solid #3363ff;
  border-radius: 5px 0px 0px 5px;
  padding: 5px 15px;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.hy_model4_top_font2 {
  border: 1px solid #3363ff;
  border-radius: 0px 5px 5px 0px;
  padding: 5px 15px;
  color: #3363ff;
  font-weight: 400;
  font-size: 14px;
}

.model4_top_add {
  background: #3363ff !important;
  color: #ffffff !important;
}

.hy_model4_youxiao_bg_active {
  /*height: 100%;*/
  border: 2px solid;
  /*border-image: linear-gradient(90deg, #2286ff 0%, #fff012 20%, #fc1311 30%, #2286ff 40%, #fff012 50%, #fc1311 60%, #2286ff 70%, #fff012 80%, #fc1311 90%, #2286ff 100%) 30 30;*/
  border-radius: 0px 5px 5px 0px;
  border-image: linear-gradient(#15abff, #15abff) 30 30;
  padding: 16px;
}

.hy_model4_youxiao_bg {
  cursor: pointer;
  border-radius: 0.375rem;
  background: #fff;
  padding: 16px;
  --un-shadow:
    var(--un-shadow-inset) 0 4px 6px -1px
      var(--un-shadow-color, rgb(0 0 0 / 0.1)),
    var(--un-shadow-inset) 0 2px 4px -2px
      var(--un-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow:
    var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
  border: 1px solid #e5e5e5;
}

.hy_model4_youxiao_bg:hover {
  background: #f5f7fa;
}

.hy_model4_youxiao_bg1 {
  width: 300px;
}

.hy_model4_youxiao_bg2 {
  width: 300px;
}

.hy_youxiao1_font1 {
  margin-bottom: 4px;
  font-weight: 400;
  font-size: 16px;
  /*padding: 10px 0px 5px 10px;*/
}

.hy_youxiao1_font2 {
  margin-bottom: 4px;
  /*padding: 2px 10px;*/
  color: #ffffff;
  font-weight: 400;
  font-size: 14px;
}

.hy_youxiao1_font3 {
  margin-bottom: 5px;
  font-weight: 400;
  /*padding: 2px 10px;*/
}

.hy_youxiao1_font4 {
  font-weight: 400;
  /*padding: 2px 10px;*/
}

.add_vip_title {
  height: 340px;
}

/*会员详情添加标签页面*/
.add_vip_title2_font2_input::-webkit-input-placeholder {
  color: rgba(187, 187, 187, 1);
  font-weight: 400;
  /* WebKit browsers */
  font-size: 14px;
}

.add_vip_title2_font2_input:-moz-placeholder {
  color: rgba(187, 187, 187, 1);
  font-weight: 400;
  /* Mozilla Firefox 4 to 18 */
  font-size: 14px;
}

.add_vip_title2_font2_input::-moz-placeholder {
  color: rgba(187, 187, 187, 1);
  font-weight: 400;
  /* Mozilla Firefox 19+ */
  font-size: 14px;
}

.add_vip_title2_font2_input:-ms-input-placeholder {
  color: rgba(187, 187, 187, 1);
  font-weight: 400;
  /* Internet Explorer 10+ */
  font-size: 14px;
}

.add_vip_title1 {
  width: 100%;
  height: 30px;
  /*background: #F6F6F6;*/
  /*border-bottom: 1px solid #E5E5E5;*/
}

.add_vip_title2 {
  /* padding: 0px 0px 0px 30px; */
  display: flex;
  box-sizing: content-box;
  /*border: 1px solid #e5e5e5;*/
  /* padding-left: 10px; */
  margin-left: 30px;
  width: 300px;
}

.add_vip_title2_font1 {
  flex: 0.2;
  margin: 0 0 10px 30px;
  height: 30px;
  color: #666666;
  font-weight: 400;
  font-size: 14px;
  line-height: 30px;
}

.add_vip_title2_font2 {
  flex: 0.6;
  height: 30px;
}

.add_vip_title2_font2_input {
  box-sizing: border-box;
  outline: none;
  border: 1px solid #e5e5e5;
  padding-left: 10px;
  height: 40px;
}

.add_vip_title2_font3 {
  background: #3363ff;
  width: 60px;
  height: 40px;
  color: white;
  font-size: 14px;
  line-height: 40px;
  text-align: center;
}

.add_vip_title3 {
  width: 100%;
  height: 50px;
  font-weight: 400;
  /*background: #E5E5E5;*/
  font-size: 12px;
  line-height: 50px;
  /*border-bottom: 1px solid #E5E5E5;*/
  /*border-top: 1px solid #E5E5E5;*/
}

.add_vip_title3_font1 {
  margin-left: 30px;
}

.add_vip_title4 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin: 15px 30px;
  max-height: 140px;
  overflow: hidden;
  overflow-y: auto;
}

.add_vip_title4::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.add_vip_title4::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.add_vip_title4::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.add_vip_title4_font0 {
  margin: 0 10px 10px 0;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px 12px;
  color: #ccc;
}

.tag_active {
  margin: 2px;
  border: 1px solid #3363ff;
  border-radius: 4px;
  padding: 8px 12px;
  color: #3363ff;
}

.hy_model1_Editing_dangan {
}

.add_dangan_info1 {
  display: flex;
  border-bottom: 1px solid #e5e5e5;
  padding: 10px 30px;
}

.add_dangan_info1_font1 {
  height: 30px;
  color: #333333;
  font-weight: 400;
  font-size: 14px;
  line-height: 30px;
}

.add_dangan_info1_font2 {
  margin-left: 20px;
  outline: none;
  border: 0;
  background: 0;
  width: 100%;
  color: #999999;
  font-size: 12px;
  line-height: 30px;
}

/*改变浏览器窗口大小的时候，表头和表身不对齐*/
.el-table th {
  display: table-cell !important;
}

/*余额明细*/
.memberBalanceStyle .el-table__body-wrapper {
  /* 120 70 32 48  15*/
  height: calc(100vh - 210px);
  overflow-y: auto;
}

.memberBalanceStyle .el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.memberBalanceStyle .el-table__body-wrapper::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.memberBalanceStyle .el-table__body-wrapper::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

/*权益明细列表*/
.memberBenfitStyle .el-table__body-wrapper {
  /* 120 70 32 48  15*/
  height: calc(100vh - 258px);
  overflow-y: auto;
}

.memberBenfitStyle .el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.memberBenfitStyle .el-table__body-wrapper::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.memberBenfitStyle .el-table__body-wrapper::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.memberBalanceFengye {
  padding: 10px 5px;
}

.memberBalanceRemark {
  overflow: hidden;
  text-overflow: ellipsis;
}
/*余额明细结束*/

.memberboxStyle .el-table__body-wrapper {
  /* 120 70 32 48  15*/
  height: 400px;
  overflow-y: auto;
}
.memberboxStyle .el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.memberboxStyle .el-table__body-wrapper::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.memberboxStyle .el-table__body-wrapper::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

/* 覆盖取消按钮默认样式 */
.el-button--default:hover {
  border-color: #dcdfe6 !important;
  background-color: #fff !important;
  color: #3363ff;
}

.entityComfirmBtn:hover {
  border-color: #3363ff !important;
  background: #3363ff !important;
  color: #fff !important;
}

.f_vip_detail_title {
  display: flex;
}

.f_vip_detail_title > div {
  padding: 5px 0;
}

.f_vip_detail_title > div:nth-child(1) {
  flex-shrink: 0;
  width: 76px;
  color: #9ca3af;
}
