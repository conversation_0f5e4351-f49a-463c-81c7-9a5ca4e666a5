/**
 * 应用状态管理器 - 防止组件间冲突和内存泄漏
 */
(function (global) {
  "use strict";

  const AppStateManager = {
    // 应用状态
    state: {
      isPayDialogOpen: false,
      isSettling: false,
      isClosing: false,
      activeOperations: new Set(),
      componentInstances: new Map(),
      pendingOperations: new Map(),
    },

    // 初始化
    init: function () {
      this.setupGlobalErrorHandling();
      this.setupMemoryMonitoring();
      console.log("AppStateManager initialized");
    },

    // 设置全局错误处理
    setupGlobalErrorHandling: function () {
      const self = this;

      // 捕获所有未处理的错误
      window.addEventListener("error", function (event) {
        console.error("Global error caught:", event.error);
        self.handleGlobalError(event.error, "window.error");
      });

      window.addEventListener("unhandledrejection", function (event) {
        console.error("Unhandled promise rejection:", event.reason);
        self.handleGlobalError(event.reason, "unhandledrejection");
      });

      // NW.js 特定错误处理
      if (typeof nw !== "undefined") {
        process.on("uncaughtException", function (error) {
          console.error("Uncaught exception:", error);
          self.handleGlobalError(error, "uncaughtException");
        });
      }
    },

    // 处理全局错误
    handleGlobalError: function (error, source) {
      console.error(`Global error from ${source}:`, error);

      // 重置所有状态
      this.resetAllStates();

      // 记录错误
      if (window.Logger) {
        window.Logger.error("Global error handled by AppStateManager", {
          error: error.toString(),
          stack: error.stack,
          source: source,
        });
      }

      // 尝试恢复应用状态
      this.attemptRecovery();
    },

    // 重置所有状态
    resetAllStates: function () {
      this.state.isPayDialogOpen = false;
      this.state.isSettling = false;
      this.state.isClosing = false;
      this.state.activeOperations.clear();
      this.state.pendingOperations.clear();
    },

    // 尝试恢复应用状态
    attemptRecovery: function () {
      try {
        // 清理可能的DOM残留
        const payWraps = document.querySelectorAll(".pay-wrap");
        payWraps.forEach((wrap) => {
          if (wrap && wrap.parentNode) {
            wrap.parentNode.removeChild(wrap);
          }
        });

        // 清理定时器
        if (window.ff_util && window.ff_util.timerManager) {
          window.ff_util.timerManager.clearAll();
        }

        console.log("App state recovery attempted");
      } catch (error) {
        console.error("Recovery failed:", error);
      }
    },

    // 注册组件实例
    registerComponent: function (name, instance) {
      this.state.componentInstances.set(name, instance);
      console.log(`Component registered: ${name}`);
    },

    // 注销组件实例
    unregisterComponent: function (name) {
      this.state.componentInstances.delete(name);
      console.log(`Component unregistered: ${name}`);
    },

    // 检查操作是否可以执行
    canExecuteOperation: function (operationName) {
      if (this.state.activeOperations.has(operationName)) {
        console.warn(`Operation ${operationName} is already active`);
        return false;
      }
      return true;
    },

    // 开始操作
    startOperation: function (operationName) {
      if (!this.canExecuteOperation(operationName)) {
        return false;
      }
      this.state.activeOperations.add(operationName);
      console.log(`Operation started: ${operationName}`);
      return true;
    },

    // 结束操作
    endOperation: function (operationName) {
      this.state.activeOperations.delete(operationName);
      console.log(`Operation ended: ${operationName}`);
    },

    // 安全执行操作
    safeExecute: function (operationName, fn, context) {
      if (!this.startOperation(operationName)) {
        return Promise.reject(
          new Error(`Operation ${operationName} already in progress`)
        );
      }

      return new Promise((resolve, reject) => {
        try {
          const result = fn.call(context);

          if (result && typeof result.then === "function") {
            // 异步操作
            result
              .then(resolve)
              .catch(reject)
              .finally(() => {
                this.endOperation(operationName);
              });
          } else {
            // 同步操作
            this.endOperation(operationName);
            resolve(result);
          }
        } catch (error) {
          this.endOperation(operationName);
          reject(error);
        }
      });
    },

    // 设置内存监控
    setupMemoryMonitoring: function () {
      if (typeof performance !== "undefined" && performance.memory) {
        setInterval(() => {
          const memory = performance.memory;
          const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
          const totalMB = Math.round(memory.totalJSHeapSize / 1024 / 1024);

          if (usedMB > 100) {
            // 超过100MB时警告
            console.warn(`High memory usage: ${usedMB}MB / ${totalMB}MB`);

            if (window.Logger) {
              window.Logger.warn("High memory usage detected", {
                used: usedMB,
                total: totalMB,
                activeOperations: Array.from(this.state.activeOperations),
              });
            }
          }
        }, 30000); // 每30秒检查一次
      }
    },

    // 强制清理
    forceCleanup: function () {
      console.log("Forcing cleanup...");

      // 重置状态
      this.resetAllStates();

      // 清理DOM
      const payWraps = document.querySelectorAll(".pay-wrap");
      payWraps.forEach((wrap) => {
        try {
          if (wrap && wrap.parentNode) {
            wrap.parentNode.removeChild(wrap);
          }
        } catch (e) {
          console.warn("Error removing pay wrap:", e);
        }
      });

      // 清理定时器
      if (window.ff_util && window.ff_util.timerManager) {
        try {
          window.ff_util.timerManager.clearAll();
        } catch (e) {
          console.warn("Error clearing timers:", e);
        }
      }

      // 强制垃圾回收（如果可用）
      if (window.gc) {
        try {
          window.gc();
        } catch (e) {
          console.warn("GC not available");
        }
      }

      console.log("Cleanup completed");
    },
  };

  // 导出到全局
  global.AppStateManager = AppStateManager;

  // 自动初始化
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", function () {
      AppStateManager.init();
    });
  } else {
    AppStateManager.init();
  }
})(window);
