var baseUrl = localStorage.getItem("fdb-domainName") + "/index.php?s=";

Number.prototype.toFixed = function (d) {
  var s = this + "";
  if (!d) d = 0;
  if (s.indexOf(".") == -1) s += ".";
  s += new Array(d + 1).join("0");
  if (new RegExp("^(-|\\+)?(\\d+(\\.\\d{0," + (d + 1) + "})?)\\d*$").test(s)) {
    var s = "0" + RegExp.$2,
      pm = RegExp.$1,
      a = RegExp.$3.length,
      b = true;
    if (a == d + 2) {
      a = s.match(/\d/g);
      if (parseInt(a[a.length - 1]) > 4) {
        for (var i = a.length - 2; i >= 0; i--) {
          a[i] = parseInt(a[i]) + 1;
          if (a[i] == 10) {
            a[i] = 0;
            b = i != 1;
          } else break;
        }
      }
      s = a.join("").replace(new RegExp("(\\d+)(\\d{" + d + "})\\d$"), "$1.$2");
    }
    if (b) s = s.substr(1);
    return (pm + s).replace(/\.$/, "");
  }
  return this + "";
};
String.prototype.toFixed = function (d) {
  var s = this + "";
  if (!d) d = 0;
  if (s.indexOf(".") == -1) s += ".";
  s += new Array(d + 1).join("0");
  if (new RegExp("^(-|\\+)?(\\d+(\\.\\d{0," + (d + 1) + "})?)\\d*$").test(s)) {
    var s = "0" + RegExp.$2,
      pm = RegExp.$1,
      a = RegExp.$3.length,
      b = true;
    if (a == d + 2) {
      a = s.match(/\d/g);
      if (parseInt(a[a.length - 1]) > 4) {
        for (var i = a.length - 2; i >= 0; i--) {
          a[i] = parseInt(a[i]) + 1;
          if (a[i] == 10) {
            a[i] = 0;
            b = i != 1;
          } else break;
        }
      }
      s = a.join("").replace(new RegExp("(\\d+)(\\d{" + d + "})\\d$"), "$1.$2");
    }
    if (b) s = s.substr(1);
    return (pm + s).replace(/\.$/, "");
  }
  return this + "";
};
//pay组件
Vue.component("app-pay", function (resolve, reject) {
  $.get("component/payDebt.html").then(function (res) {
    resolve({
      template: res,
      props: {
        billToPay: {
          type: Number,
        },
        loginInfo: {
          type: Object,
        },
        "buy-receipt": {
          type: Boolean,
        },
        "order-no": {
          type: String,
        },
        "use-card": {
          type: Boolean,
        },
        isDebtFlag: {
          type: Boolean,
        },
      },
      data: function () {
        return {
          url: baseUrl,
          userInfo: {},
          cz_qudan: true, //收款弹窗
          composeMoney: "",
          isPay: 2,
          kd_kaidanxinxi: {}, // 收款开单信息
          kd_xinxi_list: {},
          kd_xinxi_list_buyer: {},
          kd_xinxi_list_cashierInfo: {}, //
          orderDetails: [],
          payCardInfo: [],
          requisiteCard: [],
          paymentOffer: [],
          checkedOffer: [],
          cz_shou_qian: "",
          paymentCode: "", //微信支付宝付款码

          cz_chongzhika: [],

          vipPass: "", //会员密码
          returnType: 3, // 1，多余定金转入会员默认账户，2，退回现金，3，多余定金原路退回
          orderNumber: "", // 订单号
          isCard: false,
          // 键盘
          value1: "1",
          value2: "2",
          value3: "3",
          zj_price_menu: [
            { key1: "7", key2: "8", key3: "9" },
            { key1: "4", key2: "5", key3: "6" },
            { key1: "1", key2: "2", key3: "3" },
            { key1: "00", key2: "0", key3: "•" },
          ],
          zj_all_num: [
            { key: "01", value: "7" },
            { key: "02", value: "8" },
            { key: "03", value: "9" },
            { key: "11", value: "4" },
            { key: "12", value: "5" },
            { key: "13", value: "6" },
            { key: "21", value: "1" },
            { key: "22", value: "2" },
            { key: "23", value: "3" },
            { key: "31", value: "00" },
            { key: "32", value: "0" },
            { key: "33", value: "." },
          ],
          selectId: "", //选中id
          // residuebalance: 0,
          remaining: 0, // 待支付

          paperwidth: 0, //打印纸宽度
          printSet: [], // 设置打印
          endPay: 0, //是否支付完成
          showMoney: 0,
          showMoneys: 0, //展示在待支付的money
          selectedCard: [], //选中的卡数组
          SelectPreferenceSequence: -1,
          cz_shou_qians: 0.0,

          //修改员工业绩
          isModifyPerformance: false,
          isModify: true,
          zuhekaPerformance: 1, //所有业绩提成信息
          totalPerformance: {},
          performanceList: [], //业绩提成信息列表
          salesmenList: [], //销售业绩列表
          techniciansList: [], //服务人员业绩列表
          isSales: false, //选择销售弹框
          isCrafts: false, //选择服务人员弹框
          isHandelIndex: 0, //每条服务在列表中的下标
          AllSales: [], //所有销售和服务人员
          AllCrafts: [], //所有服务人员
          deductType: [
            {
              name: "比例提成",
              id: 1,
            },
            {
              name: "固定提成",
              id: 2,
            },
          ], //提成方式下拉框
          isDeductType: "", //选择的提成方式
          salesChecked: [], //选中的销售存储
          craftsChecked: [], //存储选中的服务人员
          allDelect: [],
          saveModifyArr: [], //存储修改的数据
          addArr: [], //存储增添的数据
          delArr: [], //存储删除的数据
          //员工业绩
          isModify: false,
          loginModify: undefined,
          isReturnZero: false, //抹零
          formerMoney: 0, //抹零前的金额
          formrCash: 0, //抹零前的cash
          unChangedCash: 0, //不变的cash
          smallChangeMoney: 0, //抹零金额
          customizePayType: [], //自定义记账方式数组
          custmizePayType: 0, //自定义记账payType

          //会员消费弹框
          vipComsumeConfirm: false,
          vipPassComfirm: false,
          vipPassCodeComfirm: false,
          vipPassword: "",
          vipPassCode: "",

          //欠款
          isDebt: false, //欠款弹框
          debtForm: {
            orderMoney: 0,
            payMoney: "",
            debtMoney: 0,
          },
          isDebtMoney: false,
          debtDataList: {},
        };
      },

      mounted: function () {
        this.fetchOrderDetail();
        // 获取小票样式
        this.getReceiptSet();
        this.loginModify = global.login;
        this.modifyEmployeeStatus = global.flag;
      },
      methods: {
        //监听键盘
        manualPrice: function () {
          this.cz_shou_qian = this.cz_shou_qian.replace(/[^\d\.]/g, "");
          this.cz_shou_qian = this.cz_shou_qian.replace(/^\./g, "");
          this.cz_shou_qian = this.cz_shou_qian.replace(/\.{2,}/g, ".");
          this.cz_shou_qian = this.cz_shou_qian
            .replace(".", "$#$")
            .replace(/\./g, "")
            .replace("$#$", ".");
          this.cz_shou_qian = this.cz_shou_qian.replace(
            /^(\-)*(\d+)\.(\d\d).*$/,
            "$1$2.$3"
          );
          this.cz_shou_qian = this.cz_shou_qian.replace(/^0.$/, "0.");
          if (this.cz_shou_qian > 100000.0) {
            this.cz_shou_qian = "100000.00";
          }
        },

        //欠款
        toDebt: function () {
          this.isDebt = true;
        },

        //欠款去支付关闭弹框事件
        cancelDebt: function () {
          this.isDebt = false;
        },

        //欠款input框事件
        handleDebtMoney: function (e) {
          let debtPayMoney = this.debtForm.payMoney;
          debtPayMoney = debtPayMoney.replace(/[^\d\.]/g, "");
          debtPayMoney = debtPayMoney.replace(/^\./g, "");
          debtPayMoney = debtPayMoney.replace(/\.{2,}/g, ".");
          debtPayMoney = debtPayMoney
            .replace(".", "$#$")
            .replace(/\./g, "")
            .replace("$#$", ".");
          debtPayMoney = debtPayMoney.replace(
            /^(\-)*(\d+)\.(\d\d).*$/,
            "$1$2.$3"
          );
          debtPayMoney = debtPayMoney.replace(/^0.$/, "0.");
          reg = /^\d{0,8}\.{0,1}(\d{1,2})?/g;
          let payMoneyArr = debtPayMoney.match(reg);
          let payMoney = payMoneyArr[0];

          if (payMoney) {
            let money = Math.round(Number(payMoney) * 100);
            this.debtForm.debtMoney =
              this.debtForm.orderMoney > money
                ? this.debtForm.orderMoney - money
                : 0;
            if (Number(e.target.value) === this.debtForm.orderMoney / 100) {
              this.debtForm.payMoney = e.target.value;
              return false;
            }
            this.debtForm.payMoney =
              this.debtForm.orderMoney > money
                ? debtPayMoney
                : (this.debtForm.orderMoney / 100).toFixed(2);
          }
        },

        //获取所有销售和服务人员
        getAllSales: function () {
          var _self = this;
          $.ajax({
            url: _self.url + "/android/Deduct/getStaff",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.loading = false;
                _self.AllSales = res.data;
              } else {
                _self.$message({
                  message: res.msg,
                  type: "warning",
                  duration: 1500,
                });
                _self.loading = false;
              }
            },
            error: function (error) {},
          });
        },

        //获取单据业绩提成记录
        modifyEmployeePerformance: function () {
          var _self = this;
          this.isModifyPerformance = true;

          // console.log(_self.loginInfo.merchantid,_self.loginInfo.storeid,_self.kd_xinxi_list.order_number)
          this.loading = true;
          $.ajax({
            url: _self.url + "/android/Deduct/getOrderDeductData",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              orderNo: _self.kd_xinxi_list.order_number,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.loading = false;
                _self.zuhekaPerformance = res.data.type;
                _self.totalPerformance = res.data;
                _self.performanceList = res.data.performance;
                // console.log(_self.performanceList[0].salesmen)
                for (let i = 0; i < _self.performanceList.length; i++) {
                  _self.performanceList[i].salesChecked = [];
                  _self.performanceList[i].craftsChecked = [];
                }
              } else {
                _self.$message({
                  message: res.msg,
                  type: "warning",
                  duration: 1500,
                });
                _self.loading = false;
              }
            },
            error: function (error) {},
          });
          _self.getAllSales();
        },

        //选择销售
        chooseSales: function (performanceList, index) {
          var _self = this;
          this.isSales = true;
          this.isHandelIndex = index;
          _self.salesChecked =
            _self.performanceList[_self.isHandelIndex].salesChecked;
          for (let j = 0; j < _self.AllSales.length; j++) {
            Vue.delete(_self.AllSales[j], "isDisabled");
            for (
              let i = 0;
              i < _self.performanceList[_self.isHandelIndex].salesmen.length;
              i++
            ) {
              if (
                _self.performanceList[_self.isHandelIndex].salesmen[i]
                  .staff_id == _self.AllSales[j].id
              ) {
                _self.AllSales[j].isDisabled = 1;
              }
            }
          }
        },
        //添加销售
        addSalesmen: function () {
          var _self = this;
          this.isSales = false;
          let salesmenLength =
            _self.performanceList[_self.isHandelIndex].salesmen.length;
          // console.log(_self.salesChecked,"下标")
          _self.performanceList[_self.isHandelIndex].addSalesmen = []; //存储添加的销售
          _self.performanceList[_self.isHandelIndex].salesChecked =
            _self.salesChecked;
          for (let i = 0; i < _self.salesChecked.length; i++) {
            salesmenLength += 1;
            _self.performanceList[_self.isHandelIndex].addSalesmen[i] = {
              staffName: _self.AllSales[_self.salesChecked[i]].nickname,
              lengthh: salesmenLength,
              assign: 2,
              base_amount:
                _self.performanceList[_self.isHandelIndex].base_amount,
              commission: 0.0,
              commission_proportion: 0.0,
              performance: 0.0,
              performance_proportion: 0.0,
              deduct_type: 1,
              deduct_way: 1,
              order_time: _self.totalPerformance.order_time,
              staff_id: _self.AllSales[_self.salesChecked[i]].id,
              storeid: _self.totalPerformance.storeid,
              merchantid: _self.totalPerformance.merchantid,
              id: 0,
              order_id: _self.performanceList[_self.isHandelIndex].order_id,
              order_detail_id: _self.performanceList[_self.isHandelIndex].id,
            };
          }
        },

        //选择服务人员
        chooseCrafts: function (performanceList, index) {
          var _self = this;
          this.isCrafts = true;
          this.isHandelIndex = index;
          _self.AllCrafts = _self.AllSales.filter(function (items, index, ar) {
            if (items.isTech == 2) {
              return items;
            }
          });
          _self.craftsChecked =
            _self.performanceList[_self.isHandelIndex].craftsChecked;
          for (let j = 0; j < _self.AllCrafts.length; j++) {
            Vue.delete(_self.AllCrafts[j], "isDisabled");
            for (
              let i = 0;
              i < _self.performanceList[_self.isHandelIndex].technicians.length;
              i++
            ) {
              if (
                _self.performanceList[_self.isHandelIndex].technicians[i]
                  .staff_id == _self.AllCrafts[j].id
              ) {
                _self.AllCrafts[j].isDisabled = 1;
              }
            }
          }
        },
        //添加服务人员
        addCrafts: function () {
          var _self = this;
          this.isCrafts = false;
          let craftsLength =
            _self.performanceList[_self.isHandelIndex].technicians.length;
          _self.performanceList[_self.isHandelIndex].addCrafts = [];
          _self.performanceList[_self.isHandelIndex].craftsChecked =
            _self.craftsChecked;
          for (let i = 0; i < _self.craftsChecked.length; i++) {
            craftsLength += 1;
            _self.performanceList[_self.isHandelIndex].addCrafts[i] = {
              staffName: _self.AllCrafts[_self.craftsChecked[i]].nickname,
              lengthh: craftsLength,
              assign: 2,
              base_amount:
                _self.performanceList[_self.isHandelIndex].base_amount,
              commission: 0.0,
              commission_proportion: 0.0,
              performance: 0.0,
              performance_proportion: 0.0,
              deduct_type: 2,
              deduct_way: 1,
              order_time: _self.totalPerformance.order_time,
              staff_id: _self.AllCrafts[_self.craftsChecked[i]].id,
              storeid: _self.totalPerformance.storeid,
              merchantid: _self.totalPerformance.merchantid,
              id: 0,
              order_id: _self.performanceList[_self.isHandelIndex].order_id,
              order_detail_id: _self.performanceList[_self.isHandelIndex].id,
            };
          }
        },

        //选择提成方式
        chooseDeductType: function (e, sindex, lindex) {
          this.$forceUpdate();
        },

        limitInput: function (e) {
          e.target.value = e.target.value.replace(/[^\d\.]/g, "");
          e.target.value = e.target.value.replace(/^\./g, "0.");
          e.target.value = e.target.value.replace(/\.{2,}/g, ".");
          e.target.value = e.target.value
            .replace(".", "$#$")
            .replace(/\./g, "")
            .replace("$#$", ".");
          e.target.value = e.target.value.replace(
            /^(\-)*(\d+)\.(\d\d).*$/,
            "$1$2.$3"
          );
          e.target.value = e.target.value.replace(/^0.$/, "0.");
        },
        //输入金额呈现百分比
        limitInputMoney: function (e) {
          //e.target._value 当前操作值
          //$(e.path[2]).find('input')[1].value  改变值
          //parseInt(e.path[7].children[0].childNodes[0].textContent)-1  每一项服务在服务数组中下标
          //parseInt(e.path[3].children[0].childNodes[0].textContent)-1 销售或服务人员数组中每一条数据的下标
          let performanceIndex =
            parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
          let lineIndex =
            parseInt(e.path[3].children[0].childNodes[0].textContent) - 1;
          let per = (
            (e.target._value * 10000) /
            this.performanceList[performanceIndex].base_amount
          ).toFixed(2);
          $(e.path[2]).find("input")[1].value = per;
          if (e.path[5].children[0].textContent == "选择销售") {
            this.performanceList[performanceIndex].salesmen[
              lineIndex
            ].performance_proportion = per.toString();
          } else {
            this.performanceList[performanceIndex].technicians[
              lineIndex
            ].performance_proportion = per.toString();
          }
        },
        limitInputMoneyAdd: function (e, items, index) {
          let performanceIndex =
            parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
          $(e.path[2]).find("input")[1].value = (
            (parseInt(items.performance) * 10000) /
            items.base_amount
          ).toFixed(2);
          if (e.path[5].children[0].textContent == "选择销售") {
            this.performanceList[performanceIndex].addSalesmen[
              index
            ].performance_proportion = (
              (parseInt(items.performance) * 10000) /
              items.base_amount
            ).toFixed(2);
          } else {
            this.performanceList[performanceIndex].addCrafts[
              index
            ].performance_proportion = (
              (parseInt(items.performance) * 10000) /
              items.base_amount
            ).toFixed(2);
          }
        },
        limitInputMoneyAdd1: function (e, items, index) {
          let performanceIndex =
            parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
          $(e.path[2]).find("input")[1].value = (
            (parseInt(items.commission) * 10000) /
            items.base_amount
          ).toFixed(2);
          if (e.path[5].children[0].textContent == "选择销售") {
            this.performanceList[performanceIndex].addSalesmen[
              index
            ].commission_proportion = (
              (parseInt(items.commission) * 10000) /
              items.base_amount
            ).toFixed(2);
          } else {
            this.performanceList[performanceIndex].addCrafts[
              index
            ].commission_proportion = (
              (parseInt(items.commission) * 10000) /
              items.base_amount
            ).toFixed(2);
          }
        },
        limitInputMoney1: function (e) {
          let performanceIndex =
            parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
          let lineIndex =
            parseInt(e.path[3].children[0].childNodes[0].textContent) - 1;
          let per = (
            (e.target._value * 10000) /
            this.performanceList[performanceIndex].base_amount
          ).toFixed(2);
          $(e.path[2]).find("input")[1].value = per;
          if (e.path[5].children[0].textContent == "选择销售") {
            this.performanceList[performanceIndex].salesmen[
              lineIndex
            ].commission_proportion = per.toString();
          } else {
            this.performanceList[performanceIndex].technicians[
              lineIndex
            ].commission_proportion = per.toString();
          }
        },
        //输入百分比呈现金额
        limitInputPer: function (e) {
          //e.target._value 当前操作值
          //e.path[2]).find('input')[0].value 改变值
          let performanceMoney =
            parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
          let lineIndex =
            parseInt(e.path[3].children[0].childNodes[0].textContent) - 1;
          let money = (
            (e.target._value *
              this.performanceList[performanceMoney].base_amount) /
            10000
          ).toFixed(2);
          $(e.path[2]).find("input")[0].value = money;
          if (e.path[5].children[0].textContent == "选择销售") {
            this.performanceList[performanceMoney].salesmen[
              lineIndex
            ].performance = money.toString();
          } else {
            this.performanceList[performanceMoney].technicians[
              lineIndex
            ].performance = money.toString();
          }
        },
        limitInputPer1: function (e) {
          let performanceMoney =
            parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
          let lineIndex =
            parseInt(e.path[3].children[0].childNodes[0].textContent) - 1;
          let money = (
            (e.target._value *
              this.performanceList[performanceMoney].base_amount) /
            10000
          ).toFixed(2);
          $(e.path[2]).find("input")[0].value = money;
          if (e.path[5].children[0].textContent == "选择销售") {
            this.performanceList[performanceMoney].salesmen[
              lineIndex
            ].commission = money.toString();
          } else {
            this.performanceList[performanceMoney].technicians[
              lineIndex
            ].commission = money.toString();
          }
        },
        limitInputPerAdd: function (e, items, index) {
          let performanceMoney =
            parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
          $(e.path[2]).find("input")[0].value = (
            (parseInt(items.performance_proportion) * items.base_amount) /
            10000
          ).toFixed(2);
          if (e.path[5].children[0].textContent == "选择销售") {
            this.performanceList[performanceMoney].addSalesmen[
              index
            ].performance = (
              (parseInt(items.performance_proportion) * items.base_amount) /
              10000
            ).toFixed(2);
          } else {
            this.performanceList[performanceMoney].addCrafts[
              index
            ].performance = (
              (parseInt(items.performance_proportion) * items.base_amount) /
              10000
            ).toFixed(2);
          }
        },
        limitInputPerAdd1: function (e, items, index) {
          let performanceMoney =
            parseInt(e.path[7].children[0].childNodes[0].textContent) - 1;
          $(e.path[2]).find("input")[0].value = (
            (parseInt(items.commission_proportion) * items.base_amount) /
            10000
          ).toFixed(2);
          if (e.path[5].children[0].textContent == "选择销售") {
            this.performanceList[performanceMoney].addSalesmen[
              index
            ].commission = (
              (parseInt(items.commission_proportion) * items.base_amount) /
              10000
            ).toFixed(2);
          } else {
            this.performanceList[performanceMoney].addCrafts[index].commission =
              (
                (parseInt(items.commission_proportion) * items.base_amount) /
                10000
              ).toFixed(2);
          }
        },

        //删除销售、服务人员
        delectsalesmen: function (info, index, inde) {
          var _self = this;
          _self.allDelect.push(_self.performanceList[inde].salesmen[index]);
          _self.performanceList[inde].salesmen.splice(index, 1);
          this.$forceUpdate();
        },
        delectCrafts: function (info, index, inde) {
          var _self = this;
          _self.allDelect.push(_self.performanceList[inde].technicians[index]);
          _self.performanceList[inde].technicians.splice(index, 1);
          this.$forceUpdate();
        },

        //提交保存修改的数据
        saveModify: function () {
          var _self = this;
          _self.loading = true;
          _self.saveModifyArr = [];
          _self.delArr = [];
          _self.addArr = [];
          //遍历拼接数组
          for (let i = 0; i < _self.performanceList.length; i++) {
            _self.saveModifyArr = _self.saveModifyArr.concat(
              _self.performanceList[i].salesmen,
              _self.performanceList[i].technicians
            );
            if (_self.performanceList[i].addSalesmen) {
              if (_self.performanceList[i].addCrafts) {
                _self.addArr = _self.addArr.concat(
                  _self.performanceList[i].addSalesmen,
                  _self.performanceList[i].addCrafts
                );
              } else {
                _self.addArr = _self.addArr.concat(
                  _self.performanceList[i].addSalesmen
                );
              }
            } else {
              if (_self.performanceList[i].addCrafts) {
                _self.addArr = _self.addArr.concat(
                  _self.performanceList[i].addCrafts
                );
              } else {
                _self.addArr = _self.addArr;
              }
            }
          }
          //删除多余的属性
          for (let i = 0; i < _self.addArr.length; i++) {
            Vue.delete(_self.addArr[i], "lengthh");
          }
          _self.delArr = _self.allDelect;
          _self.saveModifyArr = JSON.stringify(_self.saveModifyArr);
          _self.delArr = JSON.stringify(_self.delArr);
          _self.addArr = JSON.stringify(_self.addArr);
          $.ajax({
            url: _self.url + "/android/Deduct/saveDeductData",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              orderNo: _self.kd_xinxi_list.order_number,
              storeid: _self.loginInfo.storeid,
              addArr: _self.addArr,
              delArr: _self.delArr,
              saveArr: _self.saveModifyArr,
              nickname: _self.loginInfo.nickname,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.loading = false;
                _self.$message({
                  message: res.msg,
                  type: "success",
                  duration: 1500,
                });
                _self.isModifyPerformance = false;
              } else {
                _self.$message({
                  message: res.msg,
                  type: "warning",
                  duration: 1500,
                });
                _self.loading = false;
              }
            },
            error: function (error) {},
          });

          // console.log(_self.addArr)
        },

        //新建会员开单
        newMemberBilling: function () {
          // window.location.href = "cashier_system.html?phone=" + this.kd_xinxi_list_buyer.phone + "&flag=3";
          let href =
            "cashier_system.html?phone=" +
            this.kd_xinxi_list_buyer.phone +
            "&flag=3";
          top.app.toPage(href);
        },

        //获取自定义记账方式
        getCustomizePayType: function () {
          var _self = this;
          $.ajax({
            url: _self.url + "/android/Pay/getCustomizePayType",
            type: "POST",
            data: {
              merchantid: _self.loginInfo.merchantid,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.customizePayType = res.data;
              }
            },
            error: function (err) {},
          });
        },

        //选择自定义记账方式
        chooseCustmizePayType: function (payType) {
          this.custmizePayType = payType;
        },

        //自定义支付
        custmizePayTypeBill: function () {
          let _self = this;
          var selectOffer = _self.selectedCard.concat(_self.requisiteCard);
          var arr = [];

          if (this.custmizePayType == 0) {
            _self.$message({
              type: "warning",
              message: "请选择记账方式",
              duration: 1500,
            });
            return false;
          }

          for (var i = 0; i < selectOffer.length; i++) {
            arr.push({
              cardId: selectOffer[i].id,
              money: selectOffer[i].realPay || selectOffer[i].moneydiscount,
            });
          }
          if (
            _self.kd_xinxi_list.needVipPass &&
            _self.kd_xinxi_list.smallMoney < _self.kd_xinxi_list.vipWorth
          ) {
            _self.vipComsumeConfirm = true;
            return false;
          }

          const loading = this.$loading({
            lock: true,
            text: "支付...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          let url = "/android/pay/pay";
          if (_self.$props.isDebtFlag) {
            url = "/android/pay/payForDebt";
          }
          $.ajax({
            url: _self.url + url,
            type: "post",
            data: {
              smallChangeMoney: _self.smallChangeMoney,
              cardArr: JSON.stringify(arr),
              code: _self.paymentCode,
              debug: 0,
              orderNo: _self.kd_xinxi_list.order_number,
              payInfo: "",
              payType: _self.custmizePayType,
              returnType: _self.returnType,
              vipCode: "",

              vipPass: 0,
              merchantid: _self.userInfo.merchantid,
              storeid: _self.userInfo.storeid,
              cashier_id: _self.userInfo.id,
              shift_no: _self.userInfo.shift_no,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                loading.close();
                _self.$message({
                  type: "success",
                  message: res.msg,
                  duration: 1500,
                  onClose: function () {
                    _self.endPay = 1;
                    _self.kd_xinxi_list.state = 4;
                  },
                });
                _self.getOrderDetails();
                _self.$forceUpdate();
              } else {
                // _self.loading = false;
                loading.close();
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
        },

        //计算展示价格的值
        deleteDiscountBalance: function () {
          var _self = this;

          for (let i = 0; i < _self.orderDetails.length; i++) {
            if (_self.orderDetails[i]["equity_type"] == 1) {
              _self.showMoney += _self.orderDetails[i]["price"];
            } else if (_self.orderDetails[i]["equity_type"] == 2) {
              if (_self.orderDetails[i]["card_id"] == 0) {
                _self.showMoney += _self.orderDetails[i]["smallTotal"];
              } else {
                for (let j = 0; j < _self.payCardInfo.length; j++) {
                  if (
                    _self.orderDetails[i]["card_id"] ==
                    _self.payCardInfo[j]["id"]
                  ) {
                    _self.payCardInfo[j]["residuebalance"] -=
                      _self.orderDetails[i]["smallTotal"];
                  }
                }
              }
            } else if (_self.orderDetails[i]["equity_type"] == 3) {
              _self.showMoney += 0;
            } else {
              _self.showMoney += _self.orderDetails[i]["smallTotal"];
            }
          }

          _self.showMoneys = (_self.showMoney / 100).toFixed(2);
        },

        inputFocus: function (dom) {
          this.$nextTick(
            function () {
              dom.focus();
              // $(this.$refs.actualHarvest).focus()
            }.bind(this)
          );
        },
        bindPaymentOffer: function (item, index) {
          var _self = this;
          //定义一个变量来记录点击的折扣与待支付相见的结果
          var dealPrice = 0;
          var dealPrice1 = 0;

          var selectCardLength = _self.selectedCard.length;
          //当选中一张卡，会加入moneydiscount表示这张卡抵扣了多少钱。
          if (selectCardLength == 0 && _self.showMoney == 0) {
            //来到收款页面，选择余额。所选的都已经是折扣或者抵扣、待支付直接为0.所欲不可以点击可选卡
            _self.SelectPreferenceSequence = -1;
          } else {
            if (_self.showMoney == 0 && selectCardLength != 0) {
              //当前已经选过了权益卡并且现在的待支付为0
              //此时点击其他的卡不会有什么变化，只能点击已经选择卡，取消已经选的权益。不管已经选了几个权益卡。最多也只能选俩个。
              if (selectCardLength == 1) {
                if (_self.selectedCard[0]["id"] == item["id"]) {
                  //现在待支付是0，且选中了一个但是要取消。
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"];
                      _self.showMoney = _self.selectedCard[0]["moneydiscount"];
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      _self.selectedCard.splice(0, 1);
                      _self.SelectPreferenceSequence = -1;

                      break;
                    }
                  }
                } else {
                }
              } else {
                if (_self.selectedCard[0]["id"] == item["id"]) {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"];
                      _self.showMoney = _self.selectedCard[0]["moneydiscount"];
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      _self.selectedCard.splice(0, 1);
                      _self.SelectPreferenceSequence = -1;

                      break;
                    }
                  }
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (
                      _self.selectedCard[0]["id"] == _self.payCardInfo[i]["id"]
                    ) {
                      if (
                        _self.payCardInfo[i]["residuebalance"] >=
                        _self.showMoney
                      ) {
                        _self.payCardInfo[i]["residuebalance"] -=
                          _self.showMoney;
                        _self.selectedCard[0]["moneydiscount"] +=
                          _self.showMoney;
                        _self.showMoney = 0;
                        _self.showMoneys = _self.showMoney;
                      } else {
                        dealPrice =
                          _self.payCardInfo[i]["residuebalance"] -
                          _self.showMoney;
                        _self.payCardInfo[i]["residuebalance"] = 0;
                        _self.selectedCard[0]["moneydiscount"] +=
                          _self.payCardInfo[i]["residuebalance"];
                        _self.showMoney = Math.abs(dealPrice);
                        _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      }
                    }
                  }
                } else if (_self.selectedCard[1]["id"] == item["id"]) {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"];
                      _self.showMoney = _self.selectedCard[0]["moneydiscount"];
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      _self.selectedCard.splice(1, 1);
                      _self.SelectPreferenceSequence = -1;

                      break;
                    }
                  }
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (
                      _self.selectedCard[0]["id"] == _self.payCardInfo[i]["id"]
                    ) {
                      if (
                        _self.payCardInfo[i]["residuebalance"] >=
                        _self.showMoney
                      ) {
                        _self.payCardInfo[i]["residuebalance"] -=
                          _self.showMoney;
                        _self.selectedCard[0]["moneydiscount"] +=
                          _self.showMoney;
                        _self.showMoney = 0;
                        _self.showMoneys = _self.showMoney;
                      } else {
                        dealPrice =
                          _self.payCardInfo[i]["residuebalance"] -
                          _self.showMoney;
                        _self.payCardInfo[i]["residuebalance"] = 0;
                        _self.selectedCard[0]["moneydiscount"] +=
                          _self.payCardInfo[i]["residuebalance"];
                        _self.showMoney = Math.abs(dealPrice);
                        _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      }
                    }
                  }
                }
              }
            } else if (_self.showMoney != 0 && selectCardLength == 0) {
              for (let i = 0; i < _self.payCardInfo.length; i++) {
                if (_self.payCardInfo[i]["id"] == item["id"]) {
                  if (
                    _self.payCardInfo[i]["residuebalance"] >= _self.showMoney
                  ) {
                    _self.payCardInfo[i]["residuebalance"] -= _self.showMoney;
                    item["moneydiscount"] = _self.showMoney;
                    _self.showMoney = 0;
                    _self.showMoneys = _self.showMoney;
                  } else {
                    dealPrice = _self.payCardInfo[i]["residuebalance"] -=
                      _self.showMoney;
                    _self.payCardInfo[i]["residuebalance"] = 0;
                    item["moneydiscount"] +=
                      _self.payCardInfo[i]["residuebalance"];
                    _self.showMoney = Math.abs(dealPrice);
                    _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                  }
                  break;
                }
              }
              _self.selectedCard.push(item);
              _self.SelectPreferenceSequence = index;
            } else if (_self.showMoney != 0 && selectCardLength != 0) {
              if (selectCardLength == 1) {
                if (item["id"] == _self.selectedCard[0]["id"]) {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"];
                      _self.showMoney += _self.selectedCard[0]["moneydiscount"];
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                    }
                  }
                } else {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      if (
                        _self.payCardInfo[i]["residuebalance"] >=
                        _self.showMoney
                      ) {
                        _self.payCardInfo[i]["residuebalance"] -=
                          _self.showMoney;
                        item["moneydiscount"] = _self.showMoney;
                        _self.showMoney = 0;
                        _self.showMoneys = _self.showMoney;
                      } else {
                        dealPrice = _self.payCardInfo[i]["residuebalance"] -=
                          _self.showMoney;
                        _self.payCardInfo[i]["residuebalance"] = 0;
                        item["moneydiscount"] +=
                          _self.payCardInfo[i]["residuebalance"];
                        _self.showMoney = Math.abs(dealPrice);
                        _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      }
                      break;
                    }
                  }
                  _self.selectedCard.push(item);
                  _self.SelectPreferenceSequence = index;
                }
              } else {
                //待支付不为0 且长度为2

                if (_self.selectedCard[0]["id"] == item["id"]) {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"];
                      _self.showMoney = _self.selectedCard[0]["moneydiscount"];
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      _self.selectedCard.splice(0, 1);
                      _self.SelectPreferenceSequence = -1;

                      break;
                    }
                  }
                } else if (_self.selectedCard[1]["id"] == item["id"]) {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"];
                      _self.showMoney = _self.selectedCard[0]["moneydiscount"];
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      _self.selectedCard.splice(1, 1);
                      _self.SelectPreferenceSequence = -1;

                      break;
                    }
                  }
                }
              }
            }
          }
          _self.cz_shou_qians = _self.showMoneys;
        },

        paymentChecked: function (id) {
          for (var i = 0; i < this.checkedOffer.length; i++) {
            if (this.checkedOffer[i].id == id) {
              return true;
            }
          }
          return false;
        },

        bindPay: function (pay) {
          this.isPay = pay;
          switch (pay) {
            case 1:
              this.$nextTick(function () {
                $(this.$refs.paymentCode).focus();
              });
              break;
            case 2:
              this.$nextTick(function () {
                $(this.$refs.actualHarvest).focus();
              });
              break;
            case 4:
              this.getCustomizePayType();
              break;
          }
        },
        bindPayCard: function (item, index) {
          let _self = this;
          // console.log(item);
          // console.log(index);
          let cardPrice = (this.cz_shou_qian * 100).toFixed(0);

          if (_self.cardPayCardInfo.length >= 0) {
            let flag = false;
            let flagId = 0;
            for (let i = 0; i < _self.cardPayCardInfo.length; i++) {
              let payCardInfoItem = _self.cardPayCardInfo[i];
              if (item.id == payCardInfoItem.id) {
                flagId = i;
                flag = true;
                break;
              }
            }
            if (flag) {
              cardPrice =
                cardPrice + _self.cardPayCardInfo[flagId].residuebalance;
              // _self.cardPayCardInfo.splice(flagId, 1);
              this.cash = cardPrice;
              this.cz_shou_qian = (cardPrice / 100).toFixed(2);
              // item.checked=false;
              for (let j = 0; j < _self.payCardInfo.length; j++) {
                let item1 = _self.payCardInfo[j];
                if (item.id == item1.id) {
                  item1.checked = false;
                }
              }
              _self.$forceUpdate();
              return false;
            }

            if (item.residuebalance > 0 && cardPrice > 0) {
              item.checked = true;
              item.cardPrice =
                item.residuebalance >= cardPrice
                  ? cardPrice
                  : item.residuebalance;
              cardPrice =
                item.residuebalance >= cardPrice
                  ? 0
                  : cardPrice - item.residuebalance;
              this.cz_shou_qian = (cardPrice / 100).toFixed(2);
              this.cash = cardPrice;
              this.unChangedCash = this.cz_shou_qian;
              this.debtForm.orderMoney = this.cash;
              _self.cardPayCardInfo.push(item);
            } else {
              if (item.residuebalance == 0) {
                _self.$message({
                  message: "该卡余额不足，请充值",
                  type: "warning",
                  duration: 1500,
                });
              } else {
                _self.$message({
                  message: "已选择的卡足够支付，请确认付款",
                  type: "warning",
                  duration: 1500,
                });
              }
            }
          }
        },
        // 获取订单内容
        fetchOrderDetail: function () {
          // _self.kd_xinxi_list.orderNo
          var _self = this;
          let data = {
            merchantid: _self.userInfo.merchantid,
            orderNo: _self.orderNumber,
            storeid: _self.userInfo.storeid,
          };
          if (_self.$props.isDebtFlag) {
            data.repayment = 1;
          } else {
            data.repayment = 0;
          }
          if (_self.orderNumber.indexOf(",") == -1) {
            this.loading = true;
            $.ajax({
              url: _self.url + "/android/order/getOrderDetail",
              type: "post",
              data: data,
              success: function (res) {
                // var res = JSON.parse(res);
                if (res.code == 1) {
                  _self.loading = false;
                  _self.isPay = 2;
                  _self.kd_xinxi_list = res.data;
                  _self.kd_xinxi_list_buyer = res.data.buyer;
                  _self.kd_xinxi_list_cashierInfo = res.data.cashierInfo;
                  _self.composeMoney = res.data.toBePay / 100;
                  //订单号详情
                  // _self.orderDetails.orderInfo = res.data;
                  //会员已有卡的全部信息
                  _self.payCardInfo = res.data.payCardInfo;

                  _self.requisiteCard = res.data.requisiteCard;
                  // if (_self.cz_qudan) {
                  //     _self.$nextTick(function () {
                  //         _self.$refs.actualHarvest.focus()
                  //     }.bind(this))
                  // }
                  //当请求道数据后，开始进行先直接扣去折扣卡的优惠的价格，在展示还需要支付的钱。
                  _self.getOrderDetails(res.data.id);
                  _self.deleteDiscountBalance();
                } else {
                  _self.$message({
                    message: res.msg,
                    type: "warning",
                    duration: 1500,
                  });
                  _self.loading = false;
                }
              },
              error: function (error) {},
            });
          } else {
            data["bath"] = 1;
            $.ajax({
              url: _self.url + "/android/order/getOrderDetail",
              type: "post",
              data: data,
              success: function (res) {
                // var res = JSON.parse(res);
                if (res.code == 1) {
                  _self.debtDataList = res.data;
                  _self.kd_xinxi_list = JSON.parse(JSON.stringify(res.data));
                  _self.payCardInfo = res.data.payCardInfo;
                  _self.sellCardType = res.data.sellCardType;
                  _self.cash = res.data.payMoney;
                  _self.cz_shou_qian = (_self.cash / 100).toFixed(2);
                  _self.unChangedCash = (_self.cash / 100).toFixed(2);
                  _self.loading = false;
                } else {
                  _self.$message({
                    message: res.msg,
                    type: "warning",
                    duration: 1500,
                  });
                  _self.loading = false;
                }
              },
              error: function (error) {},
            });
          }
        },

        //获取订单详情
        getOrderDetails: function (id) {
          var _self = this;
          $.ajax({
            url: _self.url + "/android/Orderlist/OrderDetails",
            type: "POST",
            data: {
              id: id,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.kd_xinxi_list.collection_time = res.data.collection_time;
                _self.orderDetails = res.data;
                if (_self.$props.isDebtFlag) {
                  // console.log(_self.kd_xinxi_list)
                  _self.orderDetails.alreadyPay =
                    _self.kd_xinxi_list.toBePay - res.data.debt_value;
                  _self.kd_xinxi_list.toBePay = res.data.debt_value;
                  _self.cash = _self.kd_xinxi_list.toBePay;

                  _self.cz_shou_qian = String(_self.cash / 100);
                  _self.unChangedCash = String(_self.cash / 100);
                }
              }
            },
            error: function (err) {},
          });
        },

        //微信支付宝付款
        phonePay() {
          if (
            this.kd_xinxi_list.needVipPass &&
            this.kd_xinxi_list.smallMoney < this.kd_xinxi_list.vipWorth
          ) {
            this.vipComsumeConfirm = true;
          } else {
            this.pay(0);
          }
        },

        pay: function (vipPass) {
          var _self = this;
          var payType = 0; //支付类型 1微信 2支付宝 3现金 4小程序微信 5会员余额
          if (this.isPay == 0) {
            payType = 5;
          } else if (this.isPay == 1) {
            payType = 1;
          } else if (this.isPay == 2) {
            payType = 3;
          } else if (this.isPay == 4) {
            payType = _self.custmizePayType;
          }
          var selectOffer = _self.selectedCard.concat(_self.requisiteCard);
          var arr = [];

          for (var i = 0; i < selectOffer.length; i++) {
            arr.push({
              cardId: selectOffer[i].id,
              money: selectOffer[i].realPay || selectOffer[i].moneydiscount,
            });
          }

          const loading = this.$loading({
            lock: true,
            text: "支付...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          let url = "/android/pay/pay";
          if (_self.$props.isDebtFlag) {
            url = "/android/pay/payForDebt";
          }
          $.ajax({
            url: _self.url + url,
            type: "post",
            data: {
              smallChangeMoney: _self.smallChangeMoney,
              cardArr: JSON.stringify(arr),
              code: _self.paymentCode,
              debug: 0,
              orderNo: _self.kd_xinxi_list.order_number,
              payInfo: "",
              payType: payType,
              returnType: _self.returnType,
              vipCode: "",
              vipPass: vipPass,
              merchantid: _self.userInfo.merchantid,
              storeid: _self.userInfo.storeid,
              cashier_id: _self.userInfo.id,
              shift_no: _self.userInfo.shift_no,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                // _self.loading = false;
                _self.$message({
                  type: "success",
                  message: res.msg,
                  duration: 1500,
                  onClose: function () {
                    _self.endPay = 1;
                    _self.kd_xinxi_list.state = 4;
                  },
                });
                loading.close();
              } else {
                // _self.loading = false;
                loading.close();
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
        },

        //会员动态码
        vipPasswordCodeComfirm: function () {
          var reg = /^\d{14}$/g;
          // debugger
          if (reg.test(this.vipPassCode)) {
            this.pay(this.vipPassCode);
          } else {
            // console.log("密码输错了")
            this.$message({
              type: "warning",
              message: "动态码输入有误",
              duration: 1500,
            });
          }
        },

        //会员动态码
        vipPassCodeComfirmAlert: function () {
          let _self = this;
          this.vipPassCodeComfirm = true;
          setTimeout(function () {
            _self.inputFocus(_self.$refs.inputVipPasswordCode);
          }, 100);
        },

        //会员密码
        vipPasswordComfirm: function () {
          var reg = /^\d{6}$/g;
          // debugger
          if (reg.test(this.vipPassword)) {
            this.pay(this.vipPassword);
          } else {
            this.$message({
              type: "warning",
              message: "密码输入有误",
              duration: 1500,
            });
          }
        },

        //会员密码
        vipPasswordAlert: function () {
          let _self = this;
          this.vipPassComfirm = true;
          setTimeout(function () {
            _self.inputFocus(_self.$refs.inputVipPassword);
          }, 100);
        },

        // 确认收款(收银支付)
        payTheBill: function () {
          var _self = this;

          if (_self.isPay == 0 || _self.isPay == 2) {
            // if (_self.kd_xinxi_list.vip_id != 0 && (_self.requisiteCard.length > 0 || _self.selectedCard.length > 0)) {
            //     console.log('会员密码');
            //     if (_self.isPay == 0 || _self.isPay == 2) {
            //         this.$prompt('', '会员消费确认', {
            //             confirmButtonText: '确定',
            //             cancelButtonText: '取消',
            //             inputPlaceholder: "输入您的会员密码",
            //             showClose: false,
            //             cancelButtonClass: "cancelButtonClass",
            //             inputType: 'password',
            //             closeOnClickModal: false,
            //             inputPattern: /^\d{6}$/,
            //             inputErrorMessage: '密码输入有误'
            //         }).then(({value}) => {
            //             _self.pay(value)
            //         }).catch(() => {

            //         });
            //     }
            if (
              _self.kd_xinxi_list.needVipPass &&
              _self.kd_xinxi_list.smallMoney < _self.kd_xinxi_list.vipWorth
            ) {
              _self.vipComsumeConfirm = true;
            } else {
              let useMoney = Number(this.cz_shou_qian);
              let total = useMoney - Number(this.cash) / 100;
              if (!this.cz_shou_qian) {
                this.$message({
                  type: "error",
                  message: "输入支付金额",
                  duration: 1500,
                });
                return false;
              }
              if (total < 0) {
                this.$message({
                  type: "error",
                  message: "支付金额不足",
                  duration: 1500,
                });
                return false;
              }
              _self.pay();
            }
          } else if (_self.isPay == 1) {
            if (!this.paymentCode) {
              this.$message({
                type: "error",
                message: "付款码不能为空",
                duration: 1500,
              });
              return false;
            }
            _self.pay();
          }
        },

        // 支付完成
        newBilling: function () {
          this.$emit("close-pay", false);
          this.clearData();
          location.href = "cashier_system.html";
        },

        // (清空页面数据)收款数据
        clearData: function () {
          this.kd_kaidanxinxi = []; // 收款开单信息
          this.kd_xinxi_list = {};
          this.kd_xinxi_list_buyer = {};
          this.kd_xinxi_list_cashierInfo = {}; //
          this.orderDetails = [];
          this.payCardInfo = [];
          this.paymentOffer = [];
          this.checkedOffer = [];
          this.cz_shou_qian = "";
          this.paymentCode = ""; //微信支付宝付款码
          this.vipPass = ""; //会员密码
          this.returnType = 3;
        },

        cz_qudan_close: function () {
          // this.cz_qudan = false;
          this.$emit("close-pay", false);
        },

        /**键盘**/
        cz_del_all: function () {
          this.cz_shou_qian = "";
          this.inputFocus(this.$refs.actualHarvest);
        },

        cz_del_one: function () {
          this.inputFocus(this.$refs.actualHarvest);
          let input_lenth = this.cz_shou_qian.length;
          if (this.cz_shou_qian.length > 0) {
            this.cz_shou_qian = this.cz_shou_qian.slice(0, input_lenth - 1);
          }
        },

        cz_input_num: function (index, value1) {
          this.inputFocus(this.$refs.actualHarvest);
          let isdian = this.cz_shou_qian.indexOf(".");
          //console.log('dian' + isdian);
          let input_lenth = this.cz_shou_qian.length;
          let get_hlnum = index + value1;
          let cha_num = input_lenth - isdian - 1;

          if (input_lenth == 0) {
            if (get_hlnum == "33" || get_hlnum == "31" || get_hlnum == "32") {
              this.cz_shou_qian += "0.";
            } else {
              for (let i of this.zj_all_num) {
                // //console.log(i.key);
                if (i.key == get_hlnum) {
                  this.cz_shou_qian += i.value;
                }
              }
            }
          } else {
            if (isdian == -1) {
              for (let i of this.zj_all_num) {
                if (i.key == get_hlnum) {
                  this.cz_shou_qian += i.value;
                }
              }
              if (this.cz_shou_qian > 100000.0) {
                this.cz_shou_qian = "100000.00";
                this.$message({
                  showClose: true,
                  message: "最大付款金额10万",
                  type: "warning",
                });
                let that = this;
                setTimeout(function () {
                  that.zj_max_price = true;
                }, 200);
                setTimeout(function () {
                  that.zj_max_price = false;
                }, 1200);
              }
            } else if (this.cz_shou_qian == "0.") {
              if (cha_num * 1 < 2) {
                for (let i of this.zj_all_num) {
                  if (!(get_hlnum == "33")) {
                    if (i.key == get_hlnum) {
                      this.cz_shou_qian += i.value;
                    }
                  }
                }
              }
            } else {
              if (cha_num < 2) {
                for (let i of this.zj_all_num) {
                  // //console.log(i.key);
                  if (i.key == get_hlnum) {
                    this.cz_shou_qian += i.value;
                  }
                }
              }
            }
          }
        },

        // 获取小票样式
        getReceiptSet: function () {
          var _self = this;

          $.ajax({
            url: _self.url + "/android/order/getReceiptSet",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.printSet = res.data;
                _self.paperwidth = res.data.width;
              }
            },
          });
        },

        bindPrint: function () {
          // if (!LODOPbol) {
          //     return;
          // }
          var vm = this;
          if (this.orderNumber.indexOf(",") != -1) {
            // 多订单打印
            let orderNumArr = this.orderNumber.split(",");
            this.bathBindPrint(orderNumArr, 0);
          } else {
            this.$nextTick(() => {
              // 单订单打印
              if (this.printSet.length == 0) {
                Preview1();
              } else {
                // vm.printorderinfo = res.info;
                var str = $(vm.$refs.printorderstr).html();
                Preview2(str);
              }
            });
          }
        },
        bathBindPrint(orderNumArr, index) {
          var _self = this;
          let data = {
            merchantid: _self.userInfo.merchantid,
            orderNo: orderNumArr[index] + "_print",
            storeid: _self.userInfo.storeid,
          };
          this.loading = true;
          $.ajax({
            url: _self.url + "/android/order/getOrderDetail",
            type: "post",
            data: data,
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.loading = false;
                _self.isPay = 2;
                _self.kd_xinxi_list = res.data;
                _self.kd_xinxi_list.order_number = orderNumArr[index];
                _self.kd_xinxi_list_buyer = res.data.buyer;
                _self.kd_xinxi_list_cashierInfo = res.data.cashierInfo;
                _self.composeMoney = res.data.toBePay / 100;
                //当请求道数据后，开始进行先直接扣去折扣卡的优惠的价格，在展示还需要支付的钱。
                _self.bathBindPrintGetOrderDetails(
                  res.data.id,
                  orderNumArr,
                  index
                );
              } else {
                _self.$message({
                  message: res.msg,
                  type: "warning",
                  duration: 1500,
                });
                _self.loading = false;
              }
            },
            error: function (error) {},
          });
        },
        bathBindPrintGetOrderDetails(id, orderNumArr, index) {
          var _self = this;
          $.ajax({
            url: _self.url + "/android/Orderlist/OrderDetails",
            type: "POST",
            data: {
              id: id,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.kd_xinxi_list.collection_time = res.data.collection_time;
                _self.orderDetails = res.data;
                if (_self.$props.isDebtFlag) {
                  // console.log(_self.kd_xinxi_list)
                  _self.orderDetails.alreadyPay =
                    _self.kd_xinxi_list.toBePay - res.data.debt_value;
                  _self.kd_xinxi_list.toBePay = res.data.debt_value;
                  _self.cash = _self.kd_xinxi_list.toBePay;

                  _self.cz_shou_qian = String(_self.cash / 100);
                  _self.unChangedCash = String(_self.cash / 100);
                }
                _self.$nextTick(() => {
                  // 执行打印
                  if (_self.printSet.length == 0) {
                    Preview1();
                  } else {
                    // vm.printorderinfo = res.info;
                    var str = $(_self.$refs.printorderstr).html();
                    Preview2(str);
                  }
                  index++;
                  if (orderNumArr.length > index) {
                    setTimeout(() => {
                      _self.bathBindPrint(orderNumArr, index);
                    }, 10000);
                  }
                });
              }
            },
            error: function (err) {},
          });
        },
        //抹零
        returnZero: function (index) {
          let _self = this;
          if (index) {
            _self.cz_shou_qian = String(Number(_self.unChangedCash).toFixed(2));
            _self.cash = Number(_self.unChangedCash) * 100;
            _self.smallChangeMoney = 0;

            _self.isReturnZero = false;
          } else {
            if (_self.unChangedCash == _self.cz_shou_qian) {
              _self.formerMoney = Number(_self.cz_shou_qian);
              _self.formrCash = _self.cash;
            } else {
              _self.formerMoney = Number(_self.unChangedCash);
              _self.formrCash = Number(_self.unChangedCash);
            }
            let nowMoney = _self.formerMoney | 0;

            _self.smallChangeMoney =
              (_self.formerMoney * 100 - nowMoney * 100) | 0;
            _self.cz_shou_qian = String(nowMoney.toFixed(2));
            _self.cash = nowMoney * 100;

            _self.isReturnZero = true;
          }
        },
      },

      filters: {
        // 格式化充值金额/100
        filterMoney: function (money) {
          if (!isNaN(Number(money))) {
            return (money / 100).toFixed(2);
          }
          return "0";
        },
        //格式化正负号
        formatMark: function (money) {
          if (!isNaN(Number(money))) {
            if (money * 1 < 0) {
              money = money.split("-")[1];
            }
            return money;
          }
          return "0";
        },
      },

      computed: {
        kd_shishou: function () {
          if (this.isReturnZero) {
            // console.log(this.cz_shou_qian)
            // console.log(this.cash)
            let useMoney = Number(this.cz_shou_qian);
            let total = useMoney - this.cash / 100;
            if (total > 0) {
              return "找零:" + total.toFixed(2) + "元";
            } else if (total < 0) {
              return "还缺:" + (-total).toFixed(2) + "元";
            } else {
              return "不用找零";
            }
          } else {
            // console.log(this.cz_shou_qian)
            // console.log(this.cash)
            let useMoney = this.cz_shou_qian;
            let total = useMoney - this.cash / 100;
            if (total > 0) {
              return "找零:" + total.toFixed(2) + "元";
            } else if (total < 0) {
              return "还缺:" + (-total).toFixed(2) + "元";
            } else {
              return "不用找零";
            }
          }
        },
        receivableing: function () {
          let money = 0;
          if (this.orderDetails) {
            if (this.orderDetails.dismoney != "0.00") {
              money =
                money + Math.round(Number(this.orderDetails.dismoney) * 100);
            }
            if (this.orderDetails.deduction != "0.00") {
              money =
                money + Math.round(Number(this.orderDetails.deduction) * 100);
            }
            if (this.orderDetails.member_counpon_money) {
              money = money + this.orderDetails.member_counpon_money;
            }
            if (this.orderDetails.manually) {
              money = money + this.orderDetails.manually;
            }
            if (this.orderDetails.small_change_money) {
              money = money + this.orderDetails.small_change_money;
            }
            money =
              money + Math.round(Number(this.orderDetails.receivable) * 100);
            if (this.orderDetails.dispatch_fee != "0.00") {
              money =
                money -
                Math.round(Number(this.orderDetails.dispatch_fee) * 100);
            }
            return money;
          } else {
            return 0;
          }
        },

        // toBePay: function () {
        //     let product = this.orderDetails;
        //     // 所有卡项
        //     let payCardInfo = this.payCardInfo;
        //     // 选择支付充值卡
        //     let offter = this.checkedOffer;
        //     // 支付必须使用卡项
        //     let requisiteCard = this.requisiteCard;
        //
        //     let money = 0;
        //     let offterMoney = 0;
        //     let payPrince = 0;
        //     // let allPay = 0;
        //
        //     // 计算卡的剩余金额
        //     // for (var i = 0; i < product.length; i++) {
        //     //     money += product[i].num * product[i].price - product[i].reduceprice;
        //     //     if (product[i].equity_type == 2 || product[i].equity_type == 3) {
        //     //         for (var j = 0; j < requisiteCard.length; j++) {
        //     //             if (product[i].card_id == requisiteCard[j].cardId) {
        //     //                 requisiteCard[j]['money'] = parseInt(requisiteCard[j].residuebalance - product[i].smallTotal);
        //     //                 offterMoney += requisiteCard[j]['money']
        //     //             }
        //     //         }
        //     //     }
        //     // }
        //     // console.log(`money ${money}`);
        //     // console.log(`offterMoney ${offterMoney}`);
        //     // let pendingPrice = 0;
        //     //
        //     // // allPay = money - offterMoney;
        //     // this.remaining = money - offterMoney;
        //     // console.log('展示的值', this.remaining);
        //     // if (this.remaining < 0) {
        //     //     // console.log(`remaining ${remaining}`);
        //     //     this.remaining = 0;
        //     // } else {
        //     //     for (var i = 0; i < offter.length; i++) {
        //     //         if (offter[i].id == this.selectId) {
        //     //             this.remaining -= offter[i].residuebalance;
        //     //         }
        //     //     }
        //     //
        //     // }
        //
        //     // return this.remaining
        // }
      },

      watch: {
        buyReceipt: {
          handler: function (n) {
            this.cz_qudan = n;
          },
          deep: true,
          immediate: true,
        },
        loginInfo: {
          handler: function (n) {
            this.userInfo = n;
          },
          deep: true,
          immediate: true,
        },
        orderNo: {
          handler: function (n) {
            this.orderNumber = n;
          },
          deep: true,
          immediate: true,
        },
        useCard: {
          handler: function (n) {
            this.isCard = n;
          },
          deep: true,
          immediate: true,
        },
        showMoneys: {
          handler: function (n) {
            this.cz_shou_qian = this.showMoneys;
            this.cash = Number(this.showMoneys) * 100;
            this.unChangedCash = this.showMoneys;
          },
          deep: true,
          immediate: true,
        },
      },
    });
  });
});

var app = new Vue({
  el: "#app",
  data: {
    isActive: 0,
    loading: false,
    url: baseUrl,
    loginInfo: {},

    vip_source_name_model: [], //会员来源
    marketingConsultant: [], //新客归属
    vip_grade_model_name: [], //会员等级
    vip_tabel_tag: [], //会员标签
    newMember: {
      vip_v_name: "",
      vip_v_dear_name: "",
      vip_v_tel: "",
      vip_v_number: "",
      vip_source_name: "",

      vip_grade_name: "",
      vip_grade_locking: 2,
      vip_is_sex: 1,
      vip_v_ber_data: "",
      vip_v_weixin: "",
      vip_v_beizhu: "",
      curConsultant: "",
      is_vip: 0,
    },
    tempGrade: {},

    isFlag: true, //防止重复点击
    //查看会员
    vipSearch: "", //会员搜索
    currentPage: 1,
    limit: 10,
    allCount: 0,
    vip_info: [],

    //会员筛选
    is_hy_from: false,
    activeNames: ["1"],
    rateArr: [
      { word: "全部", last_time: 1 },
      { word: "1个月未消费", last_time: 2 },
      { word: "2个月未消费", last_time: 3 },
      { word: "3个月未消费", last_time: 4 },
    ],
    rateIndex: 0,
    tempRate: {
      id: -1,
    },
    rateCustomize: "",
    //频率
    frequencyArr: [
      { word: "全部", count: 1 },
      { word: "1次以内", count: 2 },
      { word: "3次以内", count: 3 },
      { word: "5次以内", count: 4 },
    ],
    frequencyIndex: 0,
    frequencyCustomize: "",
    tempFrequency: {
      id: -1,
    },
    //生日
    birthdayArr: [
      { word: "全部", birthday: 0 },
      { word: "今天", birthday: 1 },
      { word: "明天", birthday: 2 },
      { word: "本周", birthday: 3 },
      { word: "本月", birthday: 4 },
      { word: "下月", birthday: 5 },
    ],
    birthdayInput: "", //会员筛选-生日存储数据
    birthdayIndex: 0, //会员筛选-选择的日期index
    tempBirthday: {
      id: 0,
    },
    memberPointsMin: -1,
    memberPointsMax: -1,
    vip_gradeIndex: -1,
    tempVip_grade: {
      id: "",
    },
    vip_sourceIndex: -1,
    tempVip_source: {
      id: "",
    },

    consultantIndex: -1,
    tempConsultant: {
      id: "",
    },

    tabIndex: -1,
    tempTag: {
      id: "",
    },

    //会员数据
    hy_model6_bir: "1999-9-9",
    is_details_cz_open: false,
    model1_shitika_num: "",
    is_model1_vipka: false,
    is_Editing_vip_dangan: false,
    model5_title_input: "",
    is_Editing_Basic_Information: false,
    model1_sex_num: 1,
    vip_model1_info: [
      {
        name: "露露",
        vip_dengji: "普通会员",
        vip_chengzhangzhi: "111",
        vip_num: "48956321",
        vip_tel: "15806139422",
        vip_weixin: "tangyang1996",
        vip_address: "地狱",
        vip_detail_bir: "2222-22-22",
        vip_detail_address: "地狱十八层250栋666号",
        vip_details_beizhu: "我不管，我最帅",
        yingxiaoguwen: "-",
        chengweihuiyuan: "2019-03-25",
        huiyuanlaiyuan: "线上顾客",
        guishumengdian: "靖宇宣言1",

        vip_yue: "10.00",
        vip_benjin: "10.00",
        vip_detail_zengson: "10.00",
        vip_ka: "1",
        vip_youhuijuan: "1",
        vip_keyongjifen: "1",
        vip_quanyi: "1",
        vip_leijixiaofei: "10.00",
        vip_xiaofeicishu: "1",
        vip_model1_vip_title: [
          { word: "土豪" },
          { word: "土肥圆" },
          { word: "矮穷矬" },
          { word: "丑八怪" },
        ],
        vip_model1_vip_imgsrl: [
          { word: "./images/vip_1.jpg" },
          { word: "./images/vip_2.jpg" },
          { word: "./images/vip_3.jpg" },
          { word: "./images/vip_4.jpg" },
          { word: "./images/vip_5.jpg" },
        ],
        vip_model1_jiaoyi_detail: [
          {
            word: "1234",
            xiaofei_type: "类型一",
            user_name: "上帝一号",
            shops: "美容美体",
            technician: "撒旦一号",
            all_get: "500.00",
            detail_state: "以结束",
          },
          {
            word: "1234",
            xiaofei_type: "类型一",
            user_name: "上帝2号",
            shops: "美容美体",
            technician: "撒旦一号",
            all_get: "500.00",
            detail_state: "以结束",
          },
          {
            word: "1234",
            xiaofei_type: "类型一",
            user_name: "上帝3号",
            shops: "美容美体",
            technician: "撒旦一号",
            all_get: "500.00",
            detail_state: "以结束",
          },
          {
            word: "1234",
            xiaofei_type: "类型一",
            user_name: "上帝4号",
            shops: "美容美体",
            technician: "撒旦一号",
            all_get: "500.00",
            detail_state: "以结束",
          },
        ],
        vip_model1_fuwu_rizhi: [
          {
            word: "./images/keaitouxiang.jpg",
            font1: "测试0",
            font2: "biubiu",
            font3: "papa",
            font4: "axiba",
          },
          {
            word: "./images/keaitouxiang.jpg",
            font1: "测试0",
            font2: "biubiu",
            font3: "papa",
            font4: "axiba",
          },
          {
            word: "./images/keaitouxiang.jpg",
            font1: "测试0",
            font2: "biubiu",
            font3: "papa",
            font4: "axiba",
          },
          {
            word: "./images/keaitouxiang.jpg",
            font1: "测试0",
            font2: "biubiu",
            font3: "papa",
            font4: "axiba",
          },
          {
            word: "./images/keaitouxiang.jpg",
            font1: "测试0",
            font2: "biubiu",
            font3: "papa",
            font4: "axiba",
          },
        ],
      },
    ],
    vip_model2_info: [
      {
        hy_model2_server_time: "1",
        vip_model2_youxiaoqi: "2019-5-6",
        vip_daiyu: [
          { word: "指定商品8折" },
          { word: "会员专享价" },
          { word: "积分加倍" },
          { word: "超级会员日" },
          { word: "会员任务红包" },
          { word: "专属客服" },
        ],
        vip_model2_name: "露露",
        vip_model2_num: "555444666",
        vip_model2_tel: "18214890301",
        vip_model2_dengji: "钻石会员",
        hy_model2_xiaoshous: [
          { name: "霓虹" },
          { name: "青霞" },
          { name: "西门吹雪" },
        ],
        hy_model2_server_biaoti: [
          { name: "美体" },
          { name: "护甲" },
          { name: "推拿" },
          { name: "足疗" },
        ],
        hy_model2_server_biaoti_name2: [
          { name: "护甲1", price: "100.00" },
          { name: "护甲2", price: "200.00" },
          { name: "护甲3", price: "300.00" },
        ],
        hy_model2_server_biaoti_name3: [
          { name: "推拿1", price: "400.00" },
          { name: "推拿2", price: "500.00" },
          { name: "推拿3", price: "600.00" },
        ],
        hy_model2_server_biaoti_name4: [
          { name: "足疗1", price: "700.00" },
          { name: "足疗2", price: "800.00" },
          { name: "足疗3", price: "900.00" },
        ],
        hy_model2_server_biaoti_name: [
          { name: "美体1", price: "1000.00" },
          { name: "美体2", price: "2000.00" },
          { name: "美体3", price: "3000.00" },
        ],
        hy_model2_server_biaoti_name1: [
          { name: "美体1", price: "1000.00" },
          { name: "美体2", price: "2000.00" },
          { name: "美体3", price: "3000.00" },
        ],
      },
    ],
    vip_model1_bangding: false,
    vip_model_cancel_bangding: false, //实体卡解绑
    is_Editing_vip_title: false,
    //会员详情添加标签
    vip_model5_info: [{ title: "已有1" }, { title: "已有2" }],

    //会员卡数据
    hy_model4_top_num: 0,
    model4_show_youxiao: true,
    vip_model4_info: [
      {
        model4_youxiao: "3",
        model4_shixiao: "3",
        model4_vip_ka_info: [
          {
            name: "美容卡",
            ka_say: "4000元",
            kahao: "123456789789456123",
            have_time: "永久有效",
          },
          {
            name: "此卡",
            ka_say: "可以玩100次",
            kahao: "123456789789456123",
            have_time: "永久有效",
          },
          {
            name: "充值卡",
            ka_say: "40000元",
            kahao: "123456789789456123",
            have_time: "永久有效",
          },
        ],
      },
    ],
    istype: 1, //会员卡类别：1 有效卡项 2 失效卡项（必须）
    invalidMemberCard: "", //失效的会员卡信息
    invalidMemberCardLength: 0, //失效的会员卡信息
    memberCard: "", //会员卡信息
    memberCardLength: 0, //会员卡信息
    changeModelLabel: "有效", //会员卡el-radio-group的label标签
    activeColor: 0, //选中会员卡的index值，

    //会员编号实体卡明细
    isMemberShip: false, //会员编号弹出框的标志位
    memberShipData: [],
    memberShipCount: 0,
    entityStatus: 1, //实体卡状态

    //余额明细
    isMemberBalance: false, //余额明细弹框的标志位
    memberBalanceData: [], //余额明细的数据
    memberBalanceCount: 0, //余额明细的总条数
    balanceCurrentPage: 1, //余额明细的分页
    balanceLimit: 15, //余额明细的分页限制一页多少个

    //优惠券
    isMemberDiscount: false, //优惠券弹框的标志位
    memberDiscountData: [], //存储优惠券数据
    memberDiscountLabel: "未使用",
    discountType: 1, //优惠券状态 1.未使用 2.已使用 3.已过期
    discountLimit: 16, //优惠券页面限制一页多少个
    discountCurrentPage: 1, //优惠券分页
    memberDiscountCount: 0, //优惠券总条数

    //会员权益
    isMemberBenfit: false, //会员权益弹框的标志位
    memberBenfitData: [], //会员权益的数据
    memberBenfitCount: 0, //会员权益的数据
    benfitData: {}, //有效的会员权益的数据
    benfitCount: 0, //有效的会员权益个数
    benfitCurrentPage: 1, //会员权益的数据
    benfitLimit: 16, //会员权益的分页限制一页多少个
    invalidBenfitLength: 0, //失效的会员权益个数
    benfitLength: 0, //有效的会员权益个数
    memberBenfitLabel: "可使用", //

    //会员交易记录
    tradeLimit: 6, // 会员交易记录分页限制的个数
    tradeCurrentPage: 1, //会员交易记录的页数
    tradeRecondData: [], //会员交易记录的数据
    tradeRecondLength: 0, //会员交易记录的长度

    // 会员服务日志
    serviceLogLimit: 4, // 会员交易记录分页限制的个数
    serviceLogCurrentPage: 1, //会员交易记录的页数
    serviceLogData: [], //会员交易记录的数据
    serviceLogLength: 0, //会员交易记录的长度

    //会员标签
    activeTagArr: [], //选中的会员标签数组
    confirmTagArr: [], //选中的会员标签数组 放到接口里面的

    //会员成长值
    isGroupValue: false, //会员成长值弹框的标志位
    memberGroupValueData: [], //余额明细的数据
    memberGroupValueCount: 0, //余额明细的总条数
    groupValuePage: 1, //余额明细的分页
    groupValueLimit: 20, //余额明细的分页限制一页多少个

    //会员积分
    isGroupScore: false,
    memberGroupScoreData: [], //余额明细的数据
    memberGroupScoreCount: 0, //余额明细的总条数
    groupScorePage: 1, //余额明细的分页
    groupScoreLimit: 20, //余额明细的分页限制一页多少个

    //实体卡
    member_card_num: "", //  实体卡会员卡号
    member_card_voucher: "", //  实体卡标识

    //会员余额数据
    vip_model3_yue: [
      {
        name: "fuwuyi",
        kaika_time: "20",
        have_time: "5",
        validity_time: "2019-5-5",
      },
      {
        name: "fuwuyi",
        kaika_time: "20",
        have_time: "5",
        validity_time: "2019-5-5",
      },
      {
        name: "fuwuyi",
        kaika_time: "20",
        have_time: "5",
        validity_time: "2019-5-5",
      },
      {
        name: "fuwuyi",
        kaika_time: "20",
        have_time: "5",
        validity_time: "2019-5-5",
      },
      {
        name: "fuwuyi",
        kaika_time: "20",
        have_time: "5",
        validity_time: "2019-5-5",
      },
    ],
    vip_model1_xiaoshou_value: "",
    //会员模态框2优惠权益
    hy_model2_youhui_power: false,
    hy_model2_ischongxiao: false,
    hy_model2_check1: false,
    hy_model2_add_server: false,
    hy_model2_ischong_Addfuwu: 0,
    hy_model2_server_checked: false,
    //会员详情操作判断
    vip_caozuo_Name: "first",
    isfullscreen1: true,
    isfullscreen2: true,
    is_chacha1: false,
    hy_vip_detail_model: false,
    is_open_money_vip: false,

    // zdy_shaixuan6:'',
    chakan_huiyuan: false,
    vip_details_source_name: "",

    vip_source_name_model_choice: -1,
    //会员

    vip_details_grade_name: "",

    vip_grade_model_choice: -1,
    //等级变化
    vip_grade_change: false,

    vip_details_lock: false,
    vip_isinfo_save: 2,
    vip_chioce_yingu_font: "请选择",

    //以下是导航条数据

    leftMenu: [{ word: "查看客户" }, { word: "添加客户" }],

    //会员详情
    vipDetails: [],
    vipDetailsObj: {},
    vip_info_anniu1: [
      // { word: "预约", keyWord: "appointment" },
      // { word: "充值", keyWord: "recharge" },
      { word: "套餐卡", keyWord: "applyACard" },
      { word: "开单", keyWord: "createOrder" },
    ],
    vip_info_anniu2: [
      { ka1: "绑定实体卡" },
      { ka1: "解绑实体卡" },
      // {ka1: '开通付费会员'},
    ],

    //欠账
    memberDebtLabel: "待还款",
    isDebt: false, //  欠账弹框按钮
    debtCurrentPage: 1, //会员权益的数据
    debtLimit: 12, //会员权益的分页限制一页多少个
    memberDebtData: [],
    memberDebtCount: 0, //会员权益的数据
    debtData: [], //有效的会员权益的数据
    debtCount: 0, //有效的会员权益个数
    rePayData: [], //有效的会员权益的数据
    repayCount: 0, //有效的会员权益个数
    debtLength: 0, //有效的会员权益个数

    //还款
    isRechargeCard: false,
    orderNo: 0,
    billToPay: 2,
    buy_receipt: false,
    isPayDebt: false,

    //表单验证
    rules: {
      vip_v_name: [
        { required: true, message: "姓名不能为空", trigger: "blur" },
      ],
      vip_v_tel: [
        { required: true, message: "请输入手机号", trigger: "blur" },
        {
          pattern: /^1[3456789]\d{9}$/,
          message: "请输入正确格式手机号",
          trigger: "blur",
        },
      ],
      // vip_v_number: [
      //   { required: true, message: '会员编号不能为空', trigger: 'blur' }
      // ],
      vip_source_name: [
        { required: true, message: "请选择客户来源", trigger: "change" },
      ],
      vip_grade_name: [
        { required: true, message: "请选择客户等级", trigger: "change" },
      ],
    },
    // 会员还款列表
    rePayArr: [],

    showOrderCardData: false, // 查看卡项详情
    orderCardData: "", // 卡项详情数据
  },

  mounted: function () {
    this.getLoginInfo();
    this.getInit();
    this.getMember();
    this.getUrlParmsAndToCustomerDetail();
  },

  filters: {
    // 格式化充值金额/100
    filterMoney: function (money) {
      return (money / 100).toFixed(2);
    },
  },
  watch: {
    vipSearch: function (n, o) {
      if (!n && o) {
        this.currentPage = 1;
        this.getMember();
      }
    },

    rateCustomize: function () {
      if (this.rateCustomize.length > 0) {
        return (this.rateIndex = -1);
      }
    },

    frequencyCustomize: function () {
      if (this.frequencyCustomize.length > 0) {
        return (this.frequencyIndex = -1);
      }
    },
    birthdayInput: function () {
      if (this.birthdayInput.length > 0) {
        return (this.birthdayIndex = -1);
      }
    },
  },
  computed: {
    rePayArrMoney() {
      let money = 0;
      this.rePayArr.forEach((item) => {
        money += item.debt_value * 1;
      });
      return money.toFixed(2);
    },
  },
  methods: {
    getUrlParmsAndToCustomerDetail() {
      const url = window.location.href;
      const params = new URLSearchParams(url.split("?")[1]);
      const id = params.get("id");
      if (id) {
        this.$nextTick(() => {
          this.see_vip_details({ id });
        });
      }
    },
    fromTheCurrentTime(date) {
      if (!date) return "--";
      try {
        const today = new Date();
        const inputDate = new Date(date);

        if (isNaN(inputDate.getTime())) return "--";

        let years = today.getFullYear() - inputDate.getFullYear();
        let months = today.getMonth() - inputDate.getMonth();

        if (months < 0) {
          years--;
          months += 12;
        }

        // 如果日期小于当前日期，月份减1
        if (today.getDate() < inputDate.getDate()) {
          months--;
          if (months < 0) {
            years--;
            months += 12;
          }
        }

        // 计算总月份
        const totalMonths = years * 12 + months;

        // 如果小于1年，只返回月份
        if (years < 1) {
          return `${totalMonths} 个月`;
        }

        // 如果小于2年，返回"几年几个月"
        if (years < 2) {
          return `${years} 年 ${months} 个月`;
        }

        // 否则只返回"几年"
        return `${years} 年`;
      } catch (error) {
        return "--";
      }
    },
    getAge(birthday) {
      if (!birthday) return "--";
      try {
        const today = new Date();
        const birthDate = new Date(birthday);

        // 检查birthDate是否为有效日期
        if (isNaN(birthDate.getTime())) return "--";

        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        if (
          monthDiff < 0 ||
          (monthDiff === 0 && today.getDate() < birthDate.getDate())
        ) {
          age--;
        }
        return age + " 岁";
      } catch (e) {
        return "--";
      }
    },
    formatMoney(money) {
      return ff_util.formatMoney(money / 100);
    },
    calculateDaysAgo(value) {
      if (!value) return "--";
      const now = new Date();
      const lastTime = new Date(value);
      const diffTime = Math.abs(now - lastTime);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays + " 天";
    },
    getStateColor(state) {
      // 1:待付款
      // 2:待收货
      // 3:已发货
      // 4:已完成
      // 5:已取消
      switch (state) {
        case "1":
          return "warning";
        case "2":
          return "";
        case "3":
          return "";
        case "4":
          return "success";
        case "5":
          return "info";
      }
    },
    changePass() {
      var _self = this;
      var merchant_id = _self.loginInfo.merchantid;
      this.$nextTick(() => {
        const url =
          this.loginInfo["httpUrl"] +
          "/index.php?s=/wxhome/Wxbind/setMemberPassword";
        const text =
          url +
          "&memberId=" +
          this.vipDetailsObj["id"] +
          "&merchantId=" +
          merchant_id;
        this.$refs["changePass"].innerHTML = "";
        new QRCode(this.$refs["changePass"], {
          text: text,
          width: 240,
          height: 240,
          colorDark: "#000000",
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.H,
        });
      });
    },
    getFouce: function (dow) {
      this.$nextTick(
        function () {
          dow.focus();
        }.bind(this)
      );
    },

    getLoginInfo: function () {
      if (localStorage.getItem("loginInfo")) {
        this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
      } else {
        this.$message({
          type: "error",
          message: "请登录",
          duration: 1500,
        });
      }
    },

    getmenu: function (index) {
      this.isActive = index;
      if (this.isActive == 1) {
        this.getMember();
      } else {
        this.isFlag = true;
      }
    },

    getInit: function () {
      // 获取会员全部来源
      var _self = this;
      var merchant_id = _self.loginInfo.merchantid;
      var storeid = _self.loginInfo.storeid;
      $.ajax({
        url: _self.url + "/android/Member/getSourceList",
        data: {
          merchant_id: merchant_id,
        },
        type: "post",
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.vip_source_name_model = res.data;
          }
        },
      });
      // 获取会员等级
      $.ajax({
        url: _self.url + "/android/Member/getLevelList",
        type: "post",
        data: {
          merchant_id: merchant_id,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.vip_grade_model_name = res.data;
          }
        },
      });
      //新客归属
      $.ajax({
        url: _self.url + "/android/Member/getAdviser",
        type: "post",
        data: {
          merchantid: merchant_id,
          storeid: storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.marketingConsultant = res.data;
          }
        },
      });
      //会员标签
      $.ajax({
        url: _self.url + "/android/Member/getTag",
        type: "post",
        data: {
          merchant_id: merchant_id,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            res.data.forEach((item) => {
              item.isactive = 0;
            });
            _self.vip_tabel_tag = res.data;
          }
        },
      });
    },

    bindChangeGrade: function (val) {
      var self = this;
      self.tempGrade = this.vip_grade_model_name.find(function (item) {
        return item.id === val;
      });
    },

    //改变会员身份
    changeIdentity: function (val) {
      this.newMember.vip_grade_name = "";
    },

    qingkong_vip_info: function () {
      this.newMember = {
        vip_v_name: "",
        vip_v_dear_name: "",
        vip_v_tel: "",
        vip_v_number: "",
        vip_source_name: "",
        curConsultant: "",
        vip_grade_name: "",
        vip_grade_locking: 2,
        vip_is_sex: 1,
        vip_v_ber_data: "",
        vip_v_weixin: "",
        vip_v_beizhu: "",
      };
      this.getFouce(this.$refs.vip_v_name);
    },

    save_vip_info: function (formName) {
      this.$refs[formName].validate((valid) => {
        var _self = this;
        if (valid) {
          _self.addMember();
        } else {
          return false;
        }
      });
    },

    addMember: function () {
      var _self = this;
      _self.loading = true;
      $.ajax({
        url: _self.url + "/android/Member/addMember",
        type: "POST",
        data: {
          adviser: _self.newMember.curConsultant,
          birthday: _self.newMember.vip_v_ber_data,
          // growth_value: _self.tempGrade.growth_value, //会员成长值
          growth_value: "", //会员成长值
          is_level: _self.newMember.vip_grade_locking,
          member_name: _self.newMember.vip_v_name,
          member_number: _self.newMember.vip_v_number,
          member_source: _self.newMember.vip_source_name,
          phone: _self.newMember.vip_v_tel,
          remarks: _self.newMember.vip_v_beizhu,
          remarks_name: _self.newMember.vip_v_dear_name,
          sex: _self.newMember.vip_is_sex,
          wechat_number: _self.newMember.vip_v_weixin,
          store_id: _self.loginInfo.storeid,
          merchant_id: _self.loginInfo.merchantid,
          is_vip: 0,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.$message.success({
              message: res.msg,
              duration: 1500,
              onClose: function () {
                // _self.qingkong_vip_info()
                _self.$refs["ruleForm"].resetFields();
              },
            });
          } else {
            _self.$message.error({
              message: res.msg,
              duration: 1500,
            });
          }
        },
        complete: function () {
          _self.loading = false;
        },
      });
    },

    //会员列表
    getMember: function () {
      var _self = this;
      if (_self.isFlag) {
        _self.loading = true;

        $.ajax({
          url: _self.url + "/android/Member/getMember",
          type: "POST",
          data: {
            adviser: _self.tempConsultant.id, // 新客归属（筛选用）
            birthday: _self.tempBirthday.id, // 生日 0 全部 1 今天 2明天 3 本周 4 本月 5 下月
            count: _self.tempFrequency.id, // 消费次数，-1 全部 -2 一次以内 -3 三次以内 -4 五次以内，自定义直接输入次数筛选用
            starttime: _self.birthdayInput[0], // 开始时间
            endtime: _self.birthdayInput[1], // 结束时间（生日筛选 "年月日"）
            keyword: _self.vipSearch, // 会员名称、备注名、会员编号、手机号搜索
            last_time: _self.tempRate.id, // 消费期限，-1 全部、-2 一个月，-3 两个月，-4三个月，自定义直输入数字（筛选用，默认空）
            level_id: _self.tempVip_grade.id, // 等级id
            maxscore: _self.memberPointsMin, // 积分范围，最大积分
            minscore: _self.memberPointsMax, // 积分范围，最小积分
            member_source: _self.tempVip_source.id, // 来源id
            limit: _self.limit, // 分页每页条数
            page: _self.currentPage, // 分页第几页（必须）
            tab_id: _self.tempTag.id, // 标签id
            merchant_id: _self.loginInfo.merchantid, // 商户id
            storeid: _self.loginInfo.storeid,
          },
          success: function (res) {
            // var res = JSON.parse(res);

            if (res.code == 1) {
              _self.loading = false;
              // _self.isFlag = false;
              _self.vip_info = res.data;

              _self.allCount = res.count;
            } else {
              _self.loading = false;
              _self.$message.error({
                message: res.msg,
                duration: 1500,
              });
            }
          },
        });
      }
    },

    //会员搜索
    bindSearch: function () {
      if (!this.vipSearch) return;
      this.isFlag = true;
      this.currentPage = 1;
      this.getMember();
    },

    bindSearchVip: function () {
      if (this.vipSearch == "") {
        this.isFlag = true;
        this.getMember();
      }
    },

    //    以下是分页的方法
    handleSizeChange: function (val) {
      this.limit = val;
      this.currentPage = 1;
      this.isFlag = true;
      this.getMember();
      this.$nextTick(() => {
        this.$refs.vipTable.bodyWrapper.scrollTop = 0;
      });
    },
    handleCurrentChange: function (val) {
      this.currentPage = val;
      this.isFlag = true;
      this.getMember();
      this.$nextTick(() => {
        this.$refs.vipTable.bodyWrapper.scrollTop = 0;
      });
    },

    //会员筛选
    // 消费次数
    bindFrequency: function (index, item) {
      this.frequencyIndex = index;
      this.tempFrequency.id = item["count"];
      this.frequencyCustomize = "";
    },
    // 消费频率
    bindRate: function (index, item) {
      this.rateIndex = index;
      this.tempRate.id = item["last_time"];
      this.rateCustomize = "";
    },
    //会员筛选-生日筛选
    bindBirthday: function (index, item) {
      this.birthdayIndex = index;
      this.tempBirthday.id = item["birthday"];
    },
    //会员等级筛选
    bindVip_grade: function (index, item) {
      this.vip_gradeIndex = index;
      this.tempVip_grade = item;
    },
    //会员来源筛选
    bindVip_source: function (index, item) {
      this.vip_sourceIndex = index;
      this.tempVip_source = item;
    },
    //会员新客归属筛选
    bindConsultant: function (index, item) {
      this.consultantIndex = index;
      this.tempConsultant = item;
    },
    //会员标签
    bindTab: function (index, item) {
      this.tabIndex = index;
      this.tempTag = item;
    },
    //清空筛选的条件
    del_filter: function () {
      this.tempConsultant.id = "";
      this.tempBirthday.id = 0;
      this.tempFrequency.id = -1;
      this.frequencyIndex = 0;
      this.tempRate.id = -1;
      this.rateIndex = 0;
      this.birthdayIndex = 0;
      this.tempVip_grade.id = "";
      this.vip_gradeIndex = 0;
      this.tempVip_source.id = "";
      this.vip_sourceIndex = 0;
      this.consultantIndex = 0;
      this.tempTag.id = "";
      this.tabIndex = 0;
      this.rateCustomize = "";
      this.frequencyCustomize = "";
      this.birthdayInput = "";
      this.memberPointsMin = -1;
      this.memberPointsMax = -1;
      this.$nextTick(() => {
        this.$refs.memberScreening.scrollTop = 0;
      });
    },
    //会员筛选完成
    filterCarryOut: function () {
      var _self = this;
      _self.getMember();
      // _self.del_filter();
      _self.vipSearch = "";
      _self.is_hy_from = false;
      _self.isFlag = true;
    },

    hy_model6_add_dangan: function () {
      this.is_Editing_vip_dangan = true;
    },

    hy_model6_over_add_dangan: function () {
      this.is_Editing_vip_dangan = false;
    },

    back_vip_details_form_ff: function () {
      this.is_open_money_vip = false;
    },
    back_vip_model1_details: function () {
      this.is_model1_vipka = false;
      this.isMemberBalance = false;
      this.isMemberBenfit = false;
      this.isGroupValue = false; //成长值
      this.isMemberDiscount = false; //优惠券模态框
      this.isGroupScore = false; //积分模态框
      this.isDebt = false; //欠账弹框
    },

    //加载中。。。
    memberCardLoad: function () {
      return (loading = this.$loading({
        lock: true,
        text: "加载中...",
        spinner: "el-icon-loading",
        background: "rgba(255, 255, 255, 0)",
      }));
    },

    //会员卡详情
    vip_model1_show_vipka: function () {
      let _self = this;
      _self.istype = 1;
      let loading = _self.memberCardLoad();
      new Promise(function (resolve, reject) {
        $.ajax({
          url: _self.url + "/android/Member/memberCard",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            istype: _self.istype, // 类别：1 有效卡项 2 失效卡项（必须）
            limit: 100, // 分页
            page: 1, // 分页
            uid: _self.vipDetailsObj.id, //会员id
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 0) {
              resolve(res);
            }
          },
          error: function (e) {
            reject(e);
          },
        });
      }).then(function (res) {
        _self.memberCard = res.data;
        _self.memberCardLength = res.count;
        _self.istype = 2;
        $.ajax({
          url: _self.url + "/android/Member/memberCard",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            istype: _self.istype, // 类别：1 有效卡项 2 失效卡项（必须）
            limit: 100, // 分页
            page: 1, // 分页
            uid: _self.vipDetailsObj.id, //会员id
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 0) {
              _self.invalidMemberCard = res.data;
              _self.invalidMemberCardLength = res.count;
              loading.close();
              _self.is_model1_vipka = true;
            } else {
              loading.close();
              _self.$message({
                type: "error",
                message: res.msg,
                duration: 1500,
              });
            }
          },
          error: function (e) {},
        });
      });
    },

    //切换会员卡
    change_model4_top_num: function (index) {
      this.model4_show_youxiao = index == "有效";
      this.activeColor = 0;
    },
    toMoney(num) {
      return (num / 100).toFixed(2);
    },
    //选中的会员卡
    activeCard: function (index) {
      this.activeColor = index;
      let value = {};
      if (this.model4_show_youxiao) {
        value = this.memberCard[index];
      } else {
        value = this.invalidMemberCard[index];
      }
      if (!value.order_id) {
        return false;
      }
      let _self = this;
      let loading = _self.memberCardLoad();
      this.showOrderCardData = true;
      $.ajax({
        url: _self.url + "/android/Orderlist/getVipCardData",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid, // 商户id
          storeid: value.storeid, // 店铺id
          memberId: value.memberid, // 会员id
          order_id: value.order_id, // 会员id
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.orderCardData = res.data;
          } else {
            _self.orderCardData = "";
          }
          loading.close();
        },
        error: function (e) {
          loading.close();
        },
      });
    },

    //会员余额明细
    memberBalance: function () {
      let _self = this;
      let loading = _self.memberCardLoad();
      new Promise(function (resolve, reject) {
        $.ajax({
          url: _self.url + "/android/Member/getBalance",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            limit: _self.balanceLimit, // 分页
            page: _self.balanceCurrentPage, // 分页
            id: _self.vipDetailsObj.id, //会员id
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 0) {
              resolve(res);
            }
          },
          error: function (e) {
            reject(e);
          },
        });
      }).then(function (res) {
        _self.memberBalanceData = res.data;
        _self.memberBalanceCount = res.count;
        loading.close();
        _self.isMemberBalance = true;
      });
      // this.is_details_cz_open = true;
    },

    //    余额明细的分页方法
    handleBalanceSizeChange: function (val) {},

    //余额明细的分页方法
    handleBalanceCurrentChange: function (val) {
      this.balanceCurrentPage = val;
      this.memberBalance();
    },

    //查看详细信息
    handleDetail: function (index, row) {
      var id = row.order_id;
      // window.location.href = "order.html?id=" + id;
      let href = "order.html?id=" + id;
      top.app.toPage(href);
    },

    //优惠券
    discountCoupon: function () {
      var _self = this;
      this.isMemberDiscount = true;
      _self.getDiscountCoupon();
    },

    //会员积分
    accumulatePoints: function () {
      let _self = this;
      let loading = _self.memberCardLoad();
      new Promise(function (resolve, reject) {
        $.ajax({
          url: _self.url + "/android/Member/scoreDetailed",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            limit: _self.groupValueLimit, // 分页
            page: _self.groupValuePage, // 分页
            uid: _self.vipDetailsObj.id, //会员id
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 0) {
              resolve(res);
            }
          },
          error: function (e) {
            reject(e);
          },
        });
      }).then(function (res) {
        _self.memberGroupScoreData = res.data;
        _self.memberGroupScoreCount = res.count;
        loading.close();
        _self.isGroupScore = true;
      });
    },

    //    会员成长值的分页方法
    handleGroupScoreSizeChange: function (val) {},

    //会员成长值的分页方法
    handleGroupScoreCurrentChange: function (val) {
      this.groupValuePage = val;
      this.growthTap();
    },

    //切换优惠券已使用，已过期，未使用
    memberDiscountChange: function (val) {
      var _self = this;
      let loading = _self.memberCardLoad();
      if (val == "未使用") {
        _self.discountType = 1;
        _self.discountCurrentPage = 1;
        _self.getDiscountCoupon();
        loading.close();
      } else if (val == "已使用") {
        _self.discountType = 2;
        _self.discountCurrentPage = 1;
        _self.getDiscountCoupon();
        loading.close();
      } else {
        _self.discountType = 3;
        _self.discountCurrentPage = 1;
        _self.getDiscountCoupon();
        loading.close();
      }
    },

    //获取优惠券方法
    getDiscountCoupon: function () {
      var _self = this;
      $.ajax({
        url: _self.url + "/android/Member/getCouponInfo",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid, // 商户id
          type: _self.discountType,
          limit: _self.discountLimit, // 分页
          page: _self.discountCurrentPage, // 分页
          uid: _self.vipDetailsObj.id, //会员id
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 0) {
            _self.memberDiscountData = res.data;
            _self.memberDiscountCount = res.count;
          }
        },
        error: function (e) {},
      });
    },
    //分页点击
    handleDiscountSizeChange: function (val) {
      this.discountCurrentPage = val;
      this.getDiscountCoupon();
    },
    handleDiscountCurrentChange: function (val) {
      this.discountCurrentPage = val;
      this.getDiscountCoupon();
    },

    //会员权益
    memberBenfit: function () {
      let _self = this;
      _self.istype = 1;
      let loading = _self.memberCardLoad();
      new Promise(function (resolve, reject) {
        $.ajax({
          url: _self.url + "/android/Member/getEquity",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            istype: _self.istype,
            limit: _self.benfitLimit, // 分页
            page: _self.benfitCurrentPage, // 分页
            uid: _self.vipDetailsObj.id, //会员id
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 0) {
              resolve(res);
            }
          },
          error: function (e) {
            reject(e);
          },
        });
      }).then(function (res) {
        _self.benfitData = res.data;
        _self.benfitLength = res.count;
        _self.benfitCount = res.count;
        _self.istype = 2;
        $.ajax({
          url: _self.url + "/android/Member/getEquity",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            istype: _self.istype, // 类别：1 有效权益 2 失效权益（必须）
            limit: _self.benfitLimit, // 分页
            page: _self.benfitCurrentPage, // 分页
            uid: _self.vipDetailsObj.id, //会员id
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 0) {
              _self.invalidBenfitData = res.data;
              _self.invalidBenfitLength = res.count;
              loading.close();

              //把数据赋值上去
              _self.memberBenfitData = _self.benfitData;
              _self.memberBenfitCount = _self.benfitCount;
              _self.isMemberBenfit = true;
            } else {
              loading.close();
              _self.$message({
                type: "error",
                message: res.msg,
                duration: 1500,
              });
            }
          },
          error: function (e) {},
        });
      });
    },

    //    会员权益的分页方法
    handleBenfitSizeChange: function (val) {},

    //会员权益的分页方法
    handleBenfitCurrentChange: function (val) {
      this.benfitCurrentPage = val;
      this.memberBenfit();
    },

    //会员权益上边的切换可使用和已过期的方法
    memberBenfitChange: function (val) {
      let _self = this;
      let loading = _self.memberCardLoad();
      if (val == "可使用") {
        _self.memberBenfitData = _self.benfitData;
        _self.memberBenfitCount = _self.benfitCount;
        loading.close();
      } else {
        _self.memberBenfitData = _self.invalidBenfitData;
        _self.memberBenfitCount = _self.invalidBenfitLength;
        loading.close();
      }
    },

    //会员欠账
    memberDebt: function () {
      let _self = this;
      _self.is_debt = 1;
      let loading = _self.memberCardLoad();
      new Promise(function (resolve, reject) {
        $.ajax({
          url: _self.url + "/android/Member/arrearsInfo",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            storeid: _self.loginInfo.storeid, //店铺id
            is_debt: _self.is_debt,
            limit: _self.debtLimit, // 分页
            page: _self.debtCurrentPage, // 分页
            uid: _self.vipDetailsObj.id, //会员id
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 0) {
              resolve(res);
            }
          },
          error: function (e) {
            reject(e);
          },
        });
      }).then(function (res) {
        _self.debtData = res.data;
        _self.debtCount = res.count;
        _self.is_debt = 2;
        $.ajax({
          url: _self.url + "/android/Member/arrearsInfo",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            storeid: _self.loginInfo.storeid, //店铺id
            is_debt: _self.is_debt, // 类别：1 有效权益 2 失效权益（必须）
            limit: _self.debtLimit, // 分页
            page: _self.debtCurrentPage, // 分页
            uid: _self.vipDetailsObj.id, //会员id
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 0) {
              _self.rePayData = res.data;
              _self.rePayCount = res.count;
              loading.close();

              //把数据赋值上去
              _self.memberDebtData = _self.debtData;
              _self.memberDebtCount = _self.debtCount;
              _self.isDebt = true;
            } else {
              loading.close();
              _self.$message({
                type: "error",
                message: res.msg,
                duration: 1500,
              });
            }
          },
          error: function (e) {},
        });
      });
    },

    //会员欠账分页方法
    handleDebtSizeChange: function (val) {},

    //会员欠账分页方法
    handleDebtCurrentChange: function (val) {
      this.debtCurrentPage = val;
      this.memberDebt();
    },

    //会员欠款上边的切换待还款和已还款的方法
    memberDebtChange: function (val) {
      let _self = this;
      if (val == "待还款") {
        _self.memberDebtData = _self.debtData;
        _self.memberDebtCount = _self.debtCount;
      } else {
        _self.memberDebtData = _self.rePayData;
        _self.memberDebtCount = _self.rePayCount;
      }
    },

    //查看欠款订单
    seeDebtDetail: function (row) {
      location.href = "order.html?id=" + row.id;
    },

    //还款
    rePayDebt: function (row) {
      this.isPayDebt = true;
      this.orderNo = row.order_number;
      this.buy_receipt = true;
    },
    // 加入还款列表
    addToRePayArr: function (row) {
      if (this.rePayArr.length >= 10) {
        this.$message.warning("最多支持10单一起还款");
      } else {
        this.rePayArr.push({
          orderNo: row.order_number,
          debt_value: row.debt_value,
        });
      }
    },
    // 移出还款列表
    removeFromRePayArr: function (row) {
      let rePayArr = this.rePayArr.filter((item) => {
        return item["orderNo"] != row["order_number"];
      });
      this.rePayArr = rePayArr;
    },
    inRePayArr(row) {
      return this.rePayArr.some((item) => {
        return item["orderNo"] == row["order_number"];
      });
    },
    doToRePayArr() {
      let orderNo = "";
      this.rePayArr.forEach((item) => {
        if (orderNo) {
          orderNo = orderNo + "," + item["orderNo"];
        } else {
          orderNo = item["orderNo"];
        }
      });
      this.isPayDebt = true;
      this.orderNo = orderNo;
      this.buy_receipt = true;
    },
    //关闭还款
    bindClosePay: function (flag) {
      this.memberDebt();
      this.buy_receipt = flag;
      this.rePayArr = []; // 清除还款选中列表；
    },

    //获取交易记录
    gettradeRecond: function () {
      let _self = this;
      $.ajax({
        url: _self.url + "/android/Member/getOrder",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid, // 商户id
          limit: _self.tradeLimit, // 分页
          page: _self.tradeCurrentPage, // 分页
          id: _self.vipDetailsObj.id, //会员id
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 0) {
            _self.tradeRecondData = res.data;
            _self.tradeRecondLength = res.count;
            loading.close();
          } else {
            loading.close();
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
        error: function (e) {},
      });
    },

    //    会员交易记录的分页方法
    handleTradeRecondSizeChange: function (val) {},

    //会员交易记录的分页方法
    handleTradeRecondCurrentChange: function (val) {
      this.tradeCurrentPage = val;
      this.gettradeRecond();
    },

    //会员交易记录table的row-click事件
    tradeRecondRowClick: function (row, clumn, event) {
      //window.location.href = "order.html?id=" + row.id;
      let href = "order.html?id=" + row.id;
      top.app.toPage(href);
    },

    //获取服务日志
    getServiceLog: function () {
      let _self = this;

      $.ajax({
        url: _self.url + "/android/Member/getServiceLog",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid, // 商户id
          limit: _self.serviceLogLimit, // 分页
          page: _self.serviceLogCurrentPage, // 分页
          uid: _self.vipDetailsObj.id, //会员id
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 0) {
            _self.serviceLogData = res.data;
            _self.serviceLogLength = res.count;
            loading.close();
          } else {
            loading.close();
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
        error: function (e) {},
      });
    },

    //    会员服务日至的分页方法
    handleServiceLogSizeChange: function (val) {},

    //会员服务日志的分页方法
    handleServiceLogCurrentChange: function (val) {
      this.serviceCurrentPage = val;
      this.getServiceLog();
    },

    //添加标签按钮
    vip_model1_addvip_title: function () {
      let _self = this;
      if (_self.vipDetailsObj.tab_id) {
        _self.vipDetailsObj.tab_id.forEach((item) => {
          _self.vip_tabel_tag.forEach((item1) => {
            if (item.id == item1.id) {
              item1.isactive = 1;
            }
          });
        });
      }
      this.is_Editing_vip_title = true;
    },

    //添加自定义标签
    vip_add_title_to_titles: function () {
      let _self = this;
      this.$confirm("确认解绑？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "entityComfirmBtn",
        type: "primary",
      })
        .then((_) => {
          let loading = _self.memberCardLoad();
          $.ajax({
            url: _self.url + "/android/Member/adTab",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid, // 商户id
              nickname: _self.loginInfo.nickname, //选中的会员标签数组
              storetag: _self.loginInfo.storetag, //会员id
              tag_name: _self.model5_title_input, //会员id
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.$message({
                  type: "success",
                  message: "添加成功",
                  duration: 1500,
                });
                new Promise(function (resolve, reject) {
                  $.ajax({
                    url: _self.url + "/android/Member/getTag",
                    type: "post",
                    data: {
                      merchant_id: _self.loginInfo.merchantid,
                    },
                    success: function (res) {
                      // var res = JSON.parse(res);
                      if (res.code == 1) {
                        resolve(res);
                      }
                    },
                  });
                }).then(function (res) {
                  res.data.forEach((item) => {
                    item.isactive = 0;
                  });
                  _self.vip_tabel_tag = res.data;
                  _self.model5_title_input = "";
                  if (_self.vipDetailsObj.tab_id) {
                    _self.vipDetailsObj.tab_id.forEach((item) => {
                      _self.vip_tabel_tag.forEach((item1) => {
                        if (item.id == item1.id) {
                          item1.isactive = 1;
                        }
                      });
                    });
                  }
                  loading.close();
                });
              }
            },
            error: function (e) {},
          });
          done();
        })
        .catch((_) => {});
    },

    //选中会员标签
    chooseMemeberTag: function (data) {
      let _self = this;
      _self.confirmTagArr = [];
      if (_self.activeTagArr.length != 0) {
        let flag = true;
        let flagindex = 0;
        for (var i = 0; i < _self.activeTagArr.length; i++) {
          if (data.id == _self.activeTagArr[i]) {
            data.isactive = 0;
            delete _self.activeTagArr[i];
            flag = false;
            break;
          }
        }
        if (flag) {
          data.isactive = 1;
          _self.activeTagArr.push(data.id);
        }
      } else {
        data.isactive = 1;
        _self.activeTagArr.push(data.id);
      }
      _self.activeTagArr.forEach((item) => {
        if (item) {
          _self.confirmTagArr.push(item);
        }
      });
    },
    //会员标签 确定按钮
    hy_model5_save_add_titel: function () {
      let _self = this;
      let loading = _self.memberCardLoad();
      $.ajax({
        url: _self.url + "/android/Member/setTab",
        type: "post",
        // traditional:true,
        data: {
          merchantid: _self.loginInfo.merchantid, // 商户id
          // TODO 所有涉及接口传数组，应该去掉JSON.stringify，其他有待验证
          //  tabid: JSON.stringify(_self.confirmTagArr), //选中的会员标签数组
          tabid: _self.confirmTagArr, //选中的会员标签数组
          uid: _self.vipDetailsObj.id, //会员id
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.confirmTagArr = [];
            let row = { id: _self.vipDetailsObj.id };
            _self.see_vip_details(row);
            loading.close();
            _self.is_Editing_vip_title = false;
          } else {
            loading.close();
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
        error: function (e) {},
      });
    },
    hy_model5_over_add_titel: function () {
      this.is_Editing_vip_title = false;
    },
    //会员成长值
    growthTap: function () {
      let _self = this;
      let loading = _self.memberCardLoad();
      new Promise(function (resolve, reject) {
        $.ajax({
          url: _self.url + "/android/Member/getGropList",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            limit: _self.groupValueLimit, // 分页
            page: _self.groupValuePage, // 分页
            uid: _self.vipDetailsObj.id, //会员id
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 0) {
              resolve(res);
            }
          },
          error: function (e) {
            reject(e);
          },
        });
      }).then(function (res) {
        _self.memberGroupValueData = res.data;
        _self.memberGroupValueCount = res.count;
        loading.close();
        _self.isGroupValue = true;
      });
    },

    //获取会员成长值
    getGroupValue: function () {
      let _self = this;
      let loading = _self.memberCardLoad();
      new Promise(function (resolve, reject) {
        $.ajax({
          url: _self.url + "/android/Member/getGropList",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            limit: _self.groupValueLimit, // 分页
            page: _self.groupValuePage, // 分页
            uid: _self.vipDetailsObj.id, //会员id
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 0) {
              resolve(res);
            }
          },
          error: function (e) {
            reject(e);
          },
        });
      }).then(function (res) {
        _self.memberGroupValueData = res.data;
        _self.memberGroupValueCount = res.count;
        loading.close();
        _self.isGroupValue = true;
      });
    },

    //    会员成长值的分页方法
    handleGroupSizeChange: function (val) {},

    //会员成长值的分页方法
    handleGroupCurrentChange: function (val) {
      this.groupValuePage = val;
      this.growthTap();
    },

    //预约 充值 套餐卡 开单
    vip_details_chioce_ycbk: function (keyWord) {
      //0:预约  1:充值  2:套餐卡  3:开单
      let href;
      switch (keyWord) {
        case "appointment":
          // window.location.href = "reservation.html?phone=" + this.vipDetailsObj.phone + "&people=" + this.vipDetailsObj.member_name;
          href =
            "reservation.html?phone=" +
            this.vipDetailsObj.phone +
            "&people=" +
            this.vipDetailsObj.member_name;
          top.app.toPage(href);
          break;
        case "recharge":
          // window.location.href = "cashier_system.html?phone=" + this.vipDetailsObj.phone + "&flag=1";
          href =
            "cashier_system.html?phone=" + this.vipDetailsObj.phone + "&flag=1";
          top.app.toPage(href);
          break;
        case "applyACard":
          //window.location.href = "cashier_system.html?phone=" + this.vipDetailsObj.phone + "&name=" + this.vipDetailsObj.member_name + "&flag=2";
          href =
            "cashier_system.html?phone=" +
            this.vipDetailsObj.phone +
            "&name=" +
            this.vipDetailsObj.member_name +
            "&flag=2";
          top.app.toPage(href);
          break;
        case "createOrder":
          // window.location.href = "cashier_system.html?phone=" + this.vipDetailsObj.phone + "&flag=3";
          href =
            "cashier_system.html?phone=" + this.vipDetailsObj.phone + "&flag=3";
          top.app.toPage(href);
          break;
      }
    },
    //编辑会员基本档案
    hy_model6_save_add_dangan: function () {
      let _self = this;
      let loading = _self.memberCardLoad();
      $.ajax({
        url: _self.url + "/android/Member/editMember",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid, // 商户id
          birthday: _self.vipDetailsObj.birthday, // 会员生日
          address: _self.vipDetailsObj.address, // 会员相信地址
          id: _self.vipDetailsObj.id, //会员id
          remarks: _self.vipDetailsObj.remarks, //会员备注信息
          wechat_number: _self.vipDetailsObj.wechat_number, //微信号
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            loading.close();
            _self.is_Editing_vip_dangan = false;
          } else {
            loading.close();
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
        error: function (e) {},
      });
    },

    //实体卡绑定
    bindEntityCard: function () {
      let _self = this;
      if (_self.member_card_voucher) {
        let loading = _self.memberCardLoad();
        $.ajax({
          url: _self.url + "/android/Member/bindingEntityCard",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            card_num: _self.member_card_num, // 会员卡号（输入或随机生成）
            card_voucher: _self.member_card_voucher, // 	实体卡标识（即刷卡获得的内容）
            uid: _self.vipDetailsObj.id, //会员id
            storeid: _self.loginInfo.storeid, //	门店id（会员门店，总部为0）
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 1) {
              loading.close();
              _self.member_card_voucher = "";
              _self.$message({
                type: "success",
                message: "实体卡绑定成功",
                duration: 1500,
              });
              let row = {};
              row.id = _self.vipDetailsObj.id;
              _self.see_vip_details(row);
              _self.member_card_num = "";
              _self.vip_model1_bangding = false;
            } else {
              loading.close();
              _self.$message({
                type: "error",
                message: res.msg,
                duration: 1500,
              });
            }
          },
          error: function (e) {},
        });
      } else {
        _self.$message({
          type: "error",
          message: "实体卡标识不能为空",
          duration: 1500,
        });
      }
    },

    // 实体卡禁用恢复
    changeEntityStatus: function (index, row) {
      // console.log(index,row)
      // console.log(row.id,row.merchantid,row.uid)
      var _self = this;
      if (row.status == 1) {
        _self.entityStatus = 2;
      } else {
        _self.entityStatus = 1;
      }
      $.ajax({
        url: _self.url + "/android/Member/editCardStatus",
        type: "post",
        data: {
          merchantid: row.merchantid, // 商户id
          uid: _self.vipDetailsObj.id, //会员id
          cardid: row.id,
          status: _self.entityStatus,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          // if (res.code == 1) {
          //     loading.close();
          //     _self.member_card_voucher='';
          //     _self.$message({
          //         type: "success",
          //         message: "成功解除绑定",
          //         duration: 1500
          //     })
          //     _self.vip_model_cancel_bangding = false;
          // } else {
          //     loading.close();
          //     _self.$message({
          //         type: "error",
          //         message: res.msg,
          //         duration: 1500
          //     })
          // }

          _self.$message({
            type: "success",
            message: res.msg,
            duration: 1500,
          });
          _self.MembershipDetails();
        },
        error: function (e) {},
      });
    },
    //获取实体卡
    MembershipDetails: function () {
      let _self = this;
      let loading = _self.memberCardLoad();
      new Promise(function (resolve, reject) {
        $.ajax({
          url: _self.url + "/android/Member/getEntityCard",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            uid: _self.vipDetailsObj.id, //会员id
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 0) {
              resolve(res);
            }
          },
          error: function (e) {
            reject(e);
          },
        });
      }).then(function (res) {
        _self.memberShipData = res.data;
        _self.memberShipCount = res.count;
        loading.close();
        _self.isMemberShip = true;
      });
    },
    // 弹框内实体卡解绑
    untieCard: function (index, res) {
      var _self = this;
      this.$confirm("确认解绑？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "entityComfirmBtn",
        type: "primary",
      })
        .then((_) => {
          $.ajax({
            url: _self.url + "/android/Member/unboundEntityCard",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid, // 商户id
              card_voucher: res.card_voucher, //	实体卡标识（即刷卡获得的内容）
              uid: _self.vipDetailsObj.id, //会员id
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                loading.close();
                _self.$message({
                  type: "success",
                  message: "成功解除绑定",
                  duration: 1500,
                });
                let row = {};
                row.id = _self.vipDetailsObj.id;
                _self.see_vip_details(row);
                _self.vip_model_cancel_bangding = false;
                _self.MembershipDetails();
              } else {
                loading.close();
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
            error: function (e) {},
          });
          done();
        })
        .catch((_) => {});
    },

    //实体卡解绑
    cancelBindEntityCard: function () {
      let _self = this;
      this.$confirm("确认解绑？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "entityComfirmBtn",
        type: "primary",
      })
        .then((_) => {
          if (_self.member_card_voucher) {
            let loading = _self.memberCardLoad();
            $.ajax({
              url: _self.url + "/android/Member/unboundEntityCard",
              type: "post",
              data: {
                merchantid: _self.loginInfo.merchantid, // 商户id
                card_voucher: _self.member_card_voucher, //	实体卡标识（即刷卡获得的内容）
                uid: _self.vipDetailsObj.id, //会员id
              },
              success: function (res) {
                // var res = JSON.parse(res);
                if (res.code == 1) {
                  loading.close();
                  _self.member_card_voucher = "";
                  _self.$message({
                    type: "success",
                    message: "成功解除绑定",
                    duration: 1500,
                  });
                  let row = {};
                  row.id = _self.vipDetailsObj.id;
                  _self.see_vip_details(row);
                  _self.vip_model_cancel_bangding = false;
                } else {
                  loading.close();
                  _self.$message({
                    type: "error",
                    message: res.msg,
                    duration: 1500,
                  });
                }
              },
              error: function (e) {},
            });
          } else {
            _self.$message({
              type: "error",
              message: "实体卡标识不能为空",
              duration: 1500,
            });
          }
          done();
        })
        .catch((_) => {});
    },

    //绑定解绑实体卡的取消按钮
    cancelDialog: function () {
      this.vip_model_cancel_bangding = false;
      this.vip_model1_bangding = false;
      this.member_card_voucher = "";
      this.member_card_num = "";
    },

    hy_model1_save_info: function () {
      this.is_Editing_Basic_Information = false;
    },
    hy_model1_over_info: function () {
      this.is_Editing_Basic_Information = false;
    },
    model1_info_sex_chioce_nan: function () {
      this.vipDetailsObj.sex = 1;
    },
    model1_info_sex_chioce_nv: function () {
      this.vipDetailsObj.sex = 2;
    },
    bianji_jiben_xinxi: function () {
      this.is_Editing_Basic_Information = true;
    },
    back_chakan_vip: function () {
      this.hy_vip_detail_model = false;
    },
    back_vip_details: function () {
      this.is_details_cz_open = false;
    },
    hy_model2_server_tianjia_over: function () {
      this.hy_model2_add_server = false;
    },
    hy_model2_server_tianjia_save: function () {
      this.hy_model2_add_server = false;
    },
    vip_model2_add_zengson: function () {
      this.hy_model2_add_server = true;
    },
    hy_model2_tianjia_fuwu: function (index) {
      this.hy_model2_ischong_Addfuwu = index;
      if (index == 0) {
        this.vip_model2_info[0].hy_model2_server_biaoti_name =
          this.vip_model2_info[0].hy_model2_server_biaoti_name1;
      } else if (index == 1) {
        this.vip_model2_info[0].hy_model2_server_biaoti_name =
          this.vip_model2_info[0].hy_model2_server_biaoti_name2;
      } else if (index == 2) {
        this.vip_model2_info[0].hy_model2_server_biaoti_name =
          this.vip_model2_info[0].hy_model2_server_biaoti_name3;
      } else {
        this.vip_model2_info[0].hy_model2_server_biaoti_name =
          this.vip_model2_info[0].hy_model2_server_biaoti_name4;
      }
    },
    vip_model2_xuanxiao: function () {
      this.hy_model2_ischongxiao = true;
    },
    hy_model2_xiaoshou_save: function () {
      this.hy_model2_ischongxiao = false;
    },
    hy_model2_chongxiao_over: function () {
      this.hy_model2_ischongxiao = false;
    },
    hy_model2_chioce_xiaoshou: function (index) {
      alert(index);
    },
    hy_model2_quxiao_quanyi: function () {
      this.hy_model2_youhui_power = false;
    },
    hy_model2_save_quanyi: function () {
      this.hy_model2_youhui_power = false;
    },
    model1_go_fufei_vip: function (key) {
      switch (key) {
        case 0:
          this.vip_model1_bangding = true;
          break;
        case 1:
          this.vip_model_cancel_bangding = true;
          break;
        case 2:
          this.is_open_money_vip = true;
          break;
      }
    },
    model2_youhui_quanyi: function () {
      this.hy_model2_youhui_power = true;
    },
    vip_model2_del_server: function () {
      alert("删除");
    },
    hy_model2_jianshao: function () {
      if (this.vip_model2_info[0].hy_model2_server_time > 1) {
        this.vip_model2_info[0].hy_model2_server_time -= 1;
      }
    },
    hy_model2_zengjia: function () {
      if (this.vip_model2_info[0].hy_model2_server_time < 10) {
        this.vip_model2_info[0].hy_model2_server_time += 1;
      }
    },
    vip_caozuo_Click: function (tab, event) {},
    // 查看会员详情
    see_vip_details: function (row) {
      let _self = this;
      let loading = _self.memberCardLoad();
      new Promise(function (resolve, reject) {
        $.ajax({
          url: _self.url + "/android/Member/getMemberFind",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            id: row.id,
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 1) {
              resolve(res);
            }
          },
          error: function (e) {
            reject(e);
          },
        });
      }).then(function (res) {
        _self.vipDetails = {};
        _self.vipDetailsObj = res.data;

        _self.vipDetails = res.data;
        _self.gettradeRecond();
        _self.getServiceLog();
        loading.close();
        _self.hy_vip_detail_model = true;
      });
    },
    hy_shaixuan: function () {
      this.is_hy_from = true;
    },
    //会员新加的方法
    handleChange(val) {},
    bindCloseis_hy_from: function () {
      this.is_hy_from = false;
    },
    vip_chioce_yingu: function () {
      alert("营业顾问");
    },
    chioce_nvxing: function () {
      this.newMember.vip_is_sex = 1;
    },
    chioce_nanxing: function () {
      this.newMember.vip_is_sex = 2;
    },
    vipClass: function ({ row, column, rowIndex, columnIndex }) {
      return (
        "background:rgba(246,246,246,1);\n" +
        "font-size: 14px;\n" +
        "font-weight:400;\n"
      );
    },
    // 没有安装打印机
    noPrint: function () {
      let self = this;
      self.$message({
        type: "error",
        message: "打印机未准备好,无法打印",
        duration: 1500,
        onClose: function () {
          LODOPbol = false;
        },
      });
    },
  },
});
