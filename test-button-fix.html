<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-button {
            background: linear-gradient(180deg, #fff 0%, #c3cded 30%, #cbbfff 70%, #fff 100%);
            border: 2px solid transparent;
            border-radius: 12px;
            height: 51px;
            padding: 0 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            box-shadow: 0px -6px 10px -10px #385cffff, 0px 14px 30px -15px #7b9cff;
        }
        .test-button:active {
            transform: translateY(2px);
            box-shadow: 0px -3px 8px -10px #385cffff, 0px 10px 20px -15px #7b9cff;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>按钮闪退问题修复测试</h1>
        
        <div class="test-section">
            <h3>问题描述</h3>
            <p>按下鼠标左键大约500ms后松开，会导致应用闪退。</p>
            <p><strong>修复方案：</strong></p>
            <ul>
                <li>在 goSettle 方法中添加了100ms防抖处理</li>
                <li>在 app-pay 组件挂载时添加了150ms延迟，避免与按钮CSS动画冲突</li>
                <li>改进了 moveToBody 和 fetchOrderDetail 方法的错误处理</li>
                <li>添加了更详细的日志记录和异常捕获</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>测试按钮</h3>
            <p>请按住下面的按钮约500ms后松开，观察是否还会闪退：</p>
            <button class="test-button" id="testButton">测试按钮 - 模拟去结算</button>
            <div id="status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>测试日志</h3>
            <div id="logArea" class="log-area"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="test-section">
            <h3>测试说明</h3>
            <ol>
                <li>点击"测试按钮"并按住约500ms后松开</li>
                <li>观察日志输出，确认防抖机制是否正常工作</li>
                <li>检查是否还会出现应用闪退问题</li>
                <li>如果修复成功，应该看到防抖延迟执行的日志</li>
            </ol>
        </div>
    </div>

    <script>
        let pressStartTime = 0;
        let pressTimer = null;
        let logArea = document.getElementById('logArea');
        let statusDiv = document.getElementById('status');
        let testButton = document.getElementById('testButton');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logArea.textContent += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }

        function showStatus(message, type = 'success') {
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }

        function clearLog() {
            logArea.textContent = '';
        }

        // 模拟原始的 goSettle 方法（有问题的版本）
        function originalGoSettle() {
            log("🚀 ~ 原始 goSettle 方法执行");
            // 模拟立即设置 buy_receipt = true 导致的问题
            setTimeout(() => {
                log("❌ 模拟应用闪退（原始版本）");
                showStatus("模拟闪退 - 这是修复前的行为", "error");
            }, 500);
        }

        // 模拟修复后的 goSettle 方法
        function fixedGoSettle() {
            log("🚀 ~ 修复后的 goSettle 开始执行");
            
            // 防抖处理
            if (pressTimer) {
                clearTimeout(pressTimer);
                log("🚀 ~ 清除之前的防抖定时器");
            }
            
            pressTimer = setTimeout(() => {
                try {
                    log("🚀 ~ 防抖延迟后执行 buy_receipt 设置");
                    log("🚀 ~ 模拟 app-pay 组件渲染（带150ms延迟）");
                    
                    setTimeout(() => {
                        log("🚀 ~ app-pay 组件安全初始化完成");
                        showStatus("修复成功 - 没有闪退！", "success");
                    }, 150);
                    
                } catch (error) {
                    log("🚀 ~ goSettle 防抖执行出错: " + error.message);
                    showStatus("执行出错: " + error.message, "error");
                }
            }, 100); // 100ms 防抖延迟
        }

        // 按钮事件处理
        testButton.addEventListener('mousedown', (e) => {
            if (e.button === 0) { // 左键
                pressStartTime = performance.now();
                log("🚀 ~ 鼠标左键按下");
            }
        });

        testButton.addEventListener('mouseup', (e) => {
            if (e.button === 0) { // 左键
                const pressDuration = performance.now() - pressStartTime;
                log(`🚀 ~ 鼠标左键松开，按压时长: ${pressDuration.toFixed(0)}ms`);
                
                if (pressDuration >= 400 && pressDuration <= 600) {
                    log("🚀 ~ 检测到问题时长范围（400-600ms），执行修复后的方法");
                    fixedGoSettle();
                } else {
                    log("🚀 ~ 正常点击，执行修复后的方法");
                    fixedGoSettle();
                }
            }
        });

        // 初始化日志
        log("测试页面已加载，准备测试按钮修复效果");
        log("请按住测试按钮约500ms后松开");
    </script>
</body>
</html>
